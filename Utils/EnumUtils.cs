using System;
using System.ComponentModel;
using System.Linq;
using System.Reflection;

namespace HeliosETL.Utils
{
    public static class EnumUtils
    {
        /// <summary>
        /// Gets an enum value from its Description attribute
        /// </summary>
        /// <typeparam name="T">The enum type</typeparam>
        /// <param name="description">The description string to match</param>
        /// <returns>The enum value if found, otherwise throws ArgumentException</returns>
        public static T GetEnumFromDescription<T>(string description) where T : Enum
        {
            var type = typeof(T);
            
            foreach (var field in type.GetFields())
            {
                if (Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) is DescriptionAttribute attribute)
                {
                    if (attribute.Description.Equals(description, StringComparison.OrdinalIgnoreCase))
                        return (T)field.GetValue(null);
                }
                else
                {
                    if (field.Name.Equals(description, StringComparison.OrdinalIgnoreCase))
                        return (T)field.GetValue(null);
                }
            }
            
            throw new ArgumentException($"No enum value found with description '{description}' for type {typeof(T).Name}");
        }
        
        /// <summary>
        /// Tries to get an enum value from its Description attribute
        /// </summary>
        /// <typeparam name="T">The enum type</typeparam>
        /// <param name="description">The description string to match</param>
        /// <param name="result">The enum value if found</param>
        /// <returns>True if found, false otherwise</returns>
        public static bool TryGetEnumFromDescription<T>(string description, out T result) where T : Enum
        {
            try
            {
                result = GetEnumFromDescription<T>(description);
                return true;
            }
            catch
            {
                result = default(T);
                return false;
            }
        }
        
        /// <summary>
        /// Gets the description of an enum value
        /// </summary>
        /// <param name="enumValue">The enum value</param>
        /// <returns>The description string or the enum name if no description exists</returns>
        public static string GetDescription(this Enum enumValue)
        {
            var fieldInfo = enumValue.GetType().GetField(enumValue.ToString());
            var descAttribute = fieldInfo?.GetCustomAttribute<DescriptionAttribute>();
            return descAttribute?.Description ?? enumValue.ToString();
        }
    }
}