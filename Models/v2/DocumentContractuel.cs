namespace HeliosETL.Models.v2;

public class DocumentContractuel : ExternalEntity
{
    public string Libelle { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public DateTime DateDebutValidite { get; set; } = DateTime.MinValue;
    public DateTime DateFinValidite { get; set; } = DateTime.MinValue;
    public long IssueOid { get; set; }
    public long DocumentOid { get; set; }
}