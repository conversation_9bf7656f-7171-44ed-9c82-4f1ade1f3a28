namespace HeliosETL.Models.v2;

public class AbstractActivite
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Libelle { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Statut Statut { get; set; }
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleDebut { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleFin { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveDebut { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveFin { get; set; } = DateTime.MinValue;
    public DomaineMetier DomainePrincipal { get; set; }
    public HashSet<DomaineMetier> Domaines { get; set; } = new HashSet<DomaineMetier>();
    public ActiviteRelations RelationsData { get; set; } = new ActiviteRelations();
}

public class ActiviteRelations
{
    public long domaine_principal_oid;
    public long mission_type_oid;
    public long projet_type_oid;
}