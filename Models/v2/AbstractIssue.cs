namespace HeliosETL.Models.v2;

public class AbstractIssue
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Code { get; set; } = String.Empty;
    public string Sujet { get; set; } = String.Empty;
    public string Description { get; set; } = String.Empty;
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleDebut { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleFin { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveDebut { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveFin { get; set; } = DateTime.MinValue;
    public IssuePriorite Priorite { get; set; }
    public IssueStatut Statut { get; set; }
    public AbstractActivite Activite { get; set; }
    public string KindOfActivite { get; set; } = String.Empty;
    public AbstractIssue IssueParente { get; set; }
    public string KindOfIssueParente { get; set; } = String.Empty;
    public HashSet<AbstractIssue> Issues { get; set; } = new HashSet<AbstractIssue>();
    public Personne IntervenantPrincipal { get; set; }
    public Commanditaire Commanditaire { get; set; }
    public Personne Demandeur { get; set; }
    public Personne Redacteur { get; set; }
    public IssueOrigine Origine { get; set; }
    public byte Avancement { get; set; }
    public int TempsEstimeMinutes { get; set; }
    public int TempsEffectifMinutes { get; set; }
    public HashSet<AbstractIssue> RelationsSortantes { get; set; } = new HashSet<AbstractIssue>();
    public HashSet<AbstractIssue> RelationsEntrantes { get; set; } = new HashSet<AbstractIssue>();
    public HashSet<Journal> Journaux { get; set; } = new HashSet<Journal>();
    public HashSet<Personne> Intervenants { get; set; } = new HashSet<Personne>();
    public HashSet<IssuePieceJointe> PieceJointes { get; set; } = new HashSet<IssuePieceJointe>();
    public HashSet<JournalDetails> JournalDetails { get; set; } = new HashSet<JournalDetails>();
    public HashSet<DocumentContractuel> DocumentsContractuels { get; set; } = new HashSet<DocumentContractuel>();
    
    public RelationsActivite RelationsActivite { get; set; } = new RelationsActivite();
}

public class RelationsActivite
{
    public long activite_oid;
    public long issue_parente_oid;
    public long commanditaire_oid;
    public long demandeur_oid;
    public long intervenant_principal_oid;
    public long origine_oid;
    public long priorite_oid;
    public long statut_oid;
    public long redacteur_oid;
    public HashSet<long> issuesOids = new HashSet<long>();
    public HashSet<long> relationsSortantesOids = new HashSet<long>();
    public HashSet<long> relationsEntrantesOids = new HashSet<long>();
}