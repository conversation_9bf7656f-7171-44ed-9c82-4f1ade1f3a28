namespace HeliosETL.Models;

/// <summary>
/// Tracks the progress state of ETL transformation processes
/// Allows for stopping/starting ETL and recovering from failures
/// </summary>
public class ETLProgressState
{
    /// <summary>
    /// Unique identifier for this progress state record
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// Name of the transformation process (e.g., "TicketToIssueTransformation")
    /// </summary>
    public string TransformationName { get; set; } = string.Empty;
    
    /// <summary>
    /// Unique identifier for this specific transformation run
    /// </summary>
    public string RunId { get; set; } = string.Empty;
    
    /// <summary>
    /// Current state of the transformation
    /// </summary>
    public ETLProgressStatus Status { get; set; } = ETLProgressStatus.NotStarted;
    
    /// <summary>
    /// When this transformation run started
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// When this transformation run completed (success or failure)
    /// </summary>
    public DateTime? CompletedAt { get; set; }
    
    /// <summary>
    /// Last time this record was updated
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Total number of items to process
    /// </summary>
    public long TotalItems { get; set; } = 0;
    
    /// <summary>
    /// Number of items successfully processed
    /// </summary>
    public long ProcessedItems { get; set; } = 0;
    
    /// <summary>
    /// Number of items that failed processing
    /// </summary>
    public long FailedItems { get; set; } = 0;
    
    /// <summary>
    /// Number of items that were skipped
    /// </summary>
    public long SkippedItems { get; set; } = 0;
    
    /// <summary>
    /// Current phase of the transformation (Extract, Transform, Load, etc.)
    /// </summary>
    public string CurrentPhase { get; set; } = string.Empty;
    
    /// <summary>
    /// Detailed progress message
    /// </summary>
    public string? ProgressMessage { get; set; }
    
    /// <summary>
    /// Error message if transformation failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Stack trace if transformation failed
    /// </summary>
    public string? ErrorStackTrace { get; set; }
    
    /// <summary>
    /// Configuration used for this transformation (JSON format)
    /// </summary>
    public string? Configuration { get; set; }
    
    /// <summary>
    /// Additional metadata about the transformation run (JSON format)
    /// </summary>
    public string? Metadata { get; set; }
    
    /// <summary>
    /// Version of the transformation process
    /// </summary>
    public string TransformationVersion { get; set; } = "1.0";
    
    /// <summary>
    /// ID of the last successfully processed item (for resuming)
    /// </summary>
    public long? LastProcessedItemId { get; set; }
    
    /// <summary>
    /// Whether this transformation can be resumed from where it left off
    /// </summary>
    public bool CanResume { get; set; } = true;
    
    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// Maximum number of retries allowed
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}

/// <summary>
/// Status values for ETL transformation progress
/// </summary>
public enum ETLProgressStatus
{
    /// <summary>
    /// Transformation has not been started yet
    /// </summary>
    NotStarted = 0,
    
    /// <summary>
    /// Transformation is currently running
    /// </summary>
    InProgress = 1,
    
    /// <summary>
    /// Transformation completed successfully
    /// </summary>
    Completed = 2,
    
    /// <summary>
    /// Transformation failed
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// Transformation was cancelled by user
    /// </summary>
    Cancelled = 4,
    
    /// <summary>
    /// Transformation was paused and can be resumed
    /// </summary>
    Paused = 5,
    
    /// <summary>
    /// Transformation is being retried after a failure
    /// </summary>
    Retrying = 6,
    
    /// <summary>
    /// Transformation is in validation phase
    /// </summary>
    Validating = 7,
    
    /// <summary>
    /// Transformation completed with warnings
    /// </summary>
    CompletedWithWarnings = 8
}
