namespace HeliosETL.Models.v1;

public class Commandes
{
    public int IdCommandes { get; set; }
    public string CtNum { get; set; } = string.Empty;
    public string Client { get; set; } = string.Empty;
    public string Commande { get; set; } = string.Empty;
    public int IdTickets { get; set; }
    public string Pole { get; set; } = string.Empty;
    public string Commercial { get; set; } = string.Empty;
    public string Titre { get; set; } = string.Empty;
    public string ContactNom { get; set; } = string.Empty;
    public string ContactTel { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string Adresse { get; set; } = string.Empty;
    public DateTime DateLivraison { get; set; } = DateTime.MinValue;
    public bool RespectDateLivraison { get; set; } = false;
    public string Type { get; set; } = string.Empty;
    public bool Planification { get; set; } = false;
    public string Note { get; set; } = string.Empty;
    public int Bailleur { get; set; }
    public string Facture { get; set; } = string.Empty;
    public string Tech1 { get; set; } = string.Empty;
    public string Tech2 { get; set; } = string.Empty;
    public DateTime DateFin { get; set; } = DateTime.MinValue;
    public bool MajAbonne { get; set; } = false;
    public bool SuiviMail { get; set; } = false;
    public DateTime DateDemandePlanif { get; set; } = DateTime.MinValue;
    public bool AdvCheck { get; set; } = false;
    public bool AdvUpdate { get; set; } = false;
    public string AdvNote { get; set; } = string.Empty;
}