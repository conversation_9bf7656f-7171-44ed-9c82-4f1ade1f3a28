namespace HeliosETL.Models;

/// <summary>
/// Represents a relationship mapping between old system entities and new system entities
/// Used to track which old entity IDs map to which new entity IDs during ETL process
/// </summary>
public class ETLRelationship
{
    /// <summary>
    /// Unique identifier for this relationship record
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// ID of the entity in the old system (v1)
    /// </summary>
    public long OldEntityId { get; set; }
    
    /// <summary>
    /// ID of the entity in the new system (v2)
    /// </summary>
    public long NewEntityId { get; set; }
    
    /// <summary>
    /// Type of the old entity (e.g., "Tickets", "TicketsHistorique", "Categorie")
    /// </summary>
    public string OldEntityType { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of the new entity (e.g., "Issue", "Journal", "Mission")
    /// </summary>
    public string NewEntityType { get; set; } = string.Empty;
    
    /// <summary>
    /// Current state of this relationship mapping
    /// </summary>
    public ETLRelationshipState State { get; set; } = ETLRelationshipState.Pending;
    
    /// <summary>
    /// When this relationship was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// When this relationship was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Error message if the relationship mapping failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Additional metadata about the relationship (JSON format)
    /// Can store transformation-specific data
    /// </summary>
    public string? Metadata { get; set; }
    
    /// <summary>
    /// Name of the transformation process that created this relationship
    /// </summary>
    public string TransformationName { get; set; } = string.Empty;
    
    /// <summary>
    /// Version of the transformation process
    /// </summary>
    public string TransformationVersion { get; set; } = "1.0";
}

/// <summary>
/// States for ETL relationship mappings
/// </summary>
public enum ETLRelationshipState
{
    /// <summary>
    /// Relationship is pending transformation
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// Relationship mapping is in progress
    /// </summary>
    InProgress = 1,
    
    /// <summary>
    /// Relationship mapping completed successfully
    /// </summary>
    Completed = 2,
    
    /// <summary>
    /// Relationship mapping failed
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// Relationship mapping was skipped (e.g., due to validation rules)
    /// </summary>
    Skipped = 4,
    
    /// <summary>
    /// Relationship needs to be retried
    /// </summary>
    Retry = 5
}
