# ETL Tracking System Documentation

## Overview

The HeliosETL project now includes a comprehensive SQLite-based tracking system for managing ETL transformations, entity relationships, and progress state. This system enables:

- **Relationship Tracking**: Map old system entity IDs to new system entity IDs
- **Progress Management**: Track transformation progress with ability to pause/resume
- **Error Recovery**: Resume failed transformations from the last successful point
- **Audit Trail**: Complete history of all transformations and their outcomes

## Architecture

### Core Components

1. **Models**
   - `ETLRelationship`: Maps old entity IDs to new entity IDs
   - `ETLProgressState`: Tracks transformation progress and state
   - `ETLHistory`: Legacy model for backward compatibility

2. **Repositories**
   - `ETLRelationshipRepository`: CRUD operations for relationship mappings
   - `ETLProgressStateRepository`: CRUD operations for progress tracking
   - `ETLHistoryRepository`: Legacy repository (enhanced)

3. **Services**
   - `SQLiteInitializationService`: Creates and manages database schema
   - `ETLManagementService`: High-level ETL operations and coordination
   - `ETLUsageExample`: Example implementations and best practices

### Database Schema

The SQLite database (`ETL.db`) contains three main tables:

#### ETLRelationship Table
```sql
CREATE TABLE ETLRelationship (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OldEntityId INTEGER NOT NULL,           -- ID in old system (v1)
    NewEntityId INTEGER,                    -- ID in new system (v2)
    OldEntityType TEXT NOT NULL,            -- "Tickets", "TicketsHistorique", etc.
    NewEntityType TEXT NOT NULL,            -- "Issue", "Journal", etc.
    State TEXT NOT NULL DEFAULT 'Pending',  -- Pending, Completed, Failed, etc.
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    ErrorMessage TEXT,
    Metadata TEXT,                          -- JSON metadata
    TransformationName TEXT NOT NULL,
    TransformationVersion TEXT DEFAULT '1.0'
);
```

#### ETLProgressState Table
```sql
CREATE TABLE ETLProgressState (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TransformationName TEXT NOT NULL,
    RunId TEXT NOT NULL UNIQUE,
    Status TEXT NOT NULL DEFAULT 'NotStarted',
    StartedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    CompletedAt DATETIME,
    LastUpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    TotalItems INTEGER DEFAULT 0,
    ProcessedItems INTEGER DEFAULT 0,
    FailedItems INTEGER DEFAULT 0,
    SkippedItems INTEGER DEFAULT 0,
    CurrentPhase TEXT,
    ProgressMessage TEXT,
    ErrorMessage TEXT,
    ErrorStackTrace TEXT,
    Configuration TEXT,                     -- JSON configuration
    Metadata TEXT,                          -- JSON metadata
    TransformationVersion TEXT DEFAULT '1.0',
    LastProcessedItemId INTEGER,            -- For resuming
    CanResume BOOLEAN DEFAULT 1,
    RetryCount INTEGER DEFAULT 0,
    MaxRetries INTEGER DEFAULT 3
);
```

## Usage Guide

### 1. Basic Setup

The system is automatically initialized when the `Database.SetupDatabase()` method is called:

```csharp
var database = new Database();
database.SetupDatabase(); // This now includes SQLite schema initialization
```

### 2. Starting a Transformation

```csharp
var etlManager = new ETLManagementService();

// Start a new transformation
var runId = etlManager.StartTransformation(
    "TicketToIssueTransformation", 
    totalTickets.Count, 
    "1.0", 
    new Dictionary<string, object> 
    { 
        { "source", "SQL Server" }, 
        { "destination", "MySQL" }
    });
```

### 3. Tracking Entity Relationships

```csharp
// Create relationship mapping before transformation
var relationshipId = etlManager.CreateRelationship(
    ticket.IdTickets,           // Old entity ID
    "Tickets",                  // Old entity type
    "Issue",                    // New entity type
    "TicketToIssueTransformation",
    metadata);                  // Optional metadata

// Transform the entity
var issue = TransformTicketToIssue(ticket);
var newIssueId = SaveIssueToDatabase(issue);

// Complete the relationship mapping
etlManager.CompleteRelationship(relationshipId, newIssueId);
```

### 4. Updating Progress

```csharp
// Update progress during transformation
etlManager.UpdateProgress(
    runId, 
    processedCount, 
    failedCount, 
    skippedCount, 
    "Processing", 
    "Processing batch 5 of 10",
    lastProcessedItemId);
```

### 5. Handling Completion and Errors

```csharp
// On successful completion
etlManager.CompleteTransformation(runId, "All tickets transformed successfully");

// On failure
etlManager.FailTransformation(runId, ex.Message, ex.StackTrace);

// On pause (can be resumed later)
etlManager.PauseTransformation(runId, "User requested pause");
```

### 6. Resuming Failed Transformations

```csharp
// Check if transformation can be resumed
if (etlManager.CanResumeTransformation("TicketToIssueTransformation"))
{
    var latestProgress = etlManager.GetLatestProgress("TicketToIssueTransformation");
    var lastProcessedId = latestProgress.LastProcessedItemId;
    
    // Get unprocessed items starting from lastProcessedId
    var unprocessedTickets = GetTicketsAfter(lastProcessedId);
    
    // Continue processing...
}
```

### 7. Querying Relationships

```csharp
// Get new entity ID for an old entity
var issueId = etlManager.GetNewEntityId(ticketId, "Tickets");

// Get old entity ID for a new entity
var ticketId = etlManager.GetOldEntityId(issueId, "Issue");

// Get all relationships for a transformation
var relationships = etlManager.GetTransformationRelationships("TicketToIssueTransformation");
```

## Entity Type Mappings

The system tracks relationships between these entity types:

### Old System (v1) → New System (v2)
- `Tickets` → `Issue`
- `TicketsHistorique` → `Journal`
- `Categorie` → `Mission`
- `Pole` → `DomaineMetier`
- `Contacts` → `Personne`

## Best Practices

### 1. Error Handling
Always wrap transformation logic in try-catch blocks and properly mark failed relationships:

```csharp
try
{
    var relationshipId = etlManager.CreateRelationship(...);
    var newEntity = TransformEntity(oldEntity);
    var newId = SaveEntity(newEntity);
    etlManager.CompleteRelationship(relationshipId, newId);
}
catch (Exception ex)
{
    etlManager.FailRelationship(relationshipId, ex.Message);
    throw;
}
```

### 2. Progress Updates
Update progress regularly to enable meaningful resume capabilities:

```csharp
// Update every N items or every N seconds
if (processedCount % 10 == 0 || DateTime.Now - lastUpdate > TimeSpan.FromSeconds(30))
{
    etlManager.UpdateProgress(runId, processedCount, failedCount, 0, 
        currentPhase, progressMessage, lastProcessedItemId);
    lastUpdate = DateTime.Now;
}
```

### 3. Batch Processing
Process large datasets in batches to enable better progress tracking and memory management:

```csharp
const int batchSize = 100;
for (int i = 0; i < totalItems.Count; i += batchSize)
{
    var batch = totalItems.Skip(i).Take(batchSize);
    ProcessBatch(batch);
    
    etlManager.UpdateProgress(runId, i + batch.Count(), failedCount, 0, 
        "Processing", $"Completed batch {(i / batchSize) + 1}");
}
```

### 4. Metadata Usage
Store useful metadata for debugging and auditing:

```csharp
var metadata = new Dictionary<string, object>
{
    { "originalTitle", ticket.Titre },
    { "originalStatus", ticket.Status },
    { "transformedAt", DateTime.UtcNow },
    { "transformedBy", Environment.UserName },
    { "validationRules", new[] { "rule1", "rule2" } }
};
```

## Monitoring and Maintenance

### Getting Statistics
```csharp
var stats = etlManager.GetETLStatistics();
var relationshipStats = etlManager.GetRelationshipStatistics();
```

### Cleanup Old Data
```csharp
// Clean up transformations older than 30 days
var deletedCount = etlManager.CleanupOldTransformations(30);
```

### Database Validation
```csharp
var isValid = etlManager.ValidateIntegrity();
```

## Troubleshooting

### Common Issues

1. **Database Lock Errors**: Ensure connections are properly closed
2. **Missing Relationships**: Check that CreateRelationship is called before transformation
3. **Resume Failures**: Verify LastProcessedItemId is being updated correctly
4. **Performance Issues**: Ensure indexes are created (handled automatically)

### Useful Queries

See `SQL/schema.sql` for example queries for monitoring and troubleshooting.

## Integration with Existing Code

The new tracking system is designed to integrate seamlessly with existing ETL transformers. Simply:

1. Inject `ETLManagementService` into your transformers
2. Add tracking calls around your existing transformation logic
3. Use the relationship mappings to handle entity references

For complete examples, see `Services/Examples/ETLUsageExample.cs`.
