### 📋 **Junction Tables Identified**
- `hls_activite_has_domaine` (AbstractActivite ↔ DomaineMetier) Fields: activite_oid, domaine_oid
- `hls_commanditaire_has_personne` (Commanditaire ↔ Personne) Fields: commanditaire_oid, personne_oid
- `hls_document_type_has_domaine_metier` (TypeDocument ↔ DomaineMetier) Fields: document_type_oid, domaine_metier_oid
- `hls_issue_relation` (Issue ↔ Issue) Fields: cible_oid, source_oid
- `hls_issue_has_intervenant` (Issue ↔ Personne) Fields: issue_oid, personne_oid
- `hls_issue_origine_type_has_domaine_metier` (IssueOrigine ↔ DomaineMetier) Fields: issue_origine_oid, domaine_metier_oid
- `hls_issue_priorite_has_domaine_metier` (IssuePriorite ↔ DomaineMetier) Fields: issue_priorite_oid, domaine_metier_oid
- `hls_issue_statut_has_domaine_metier` (IssueStatut ↔ DomaineMetier) Fields: issue_statut_oid, domaine_metier_oid
- `hls_mission_type_has_domaine_metier` (TypeMission ↔ DomaineMetier) Fields: mission_type_oid, domaine_metier_oid
- `hls_personne_type_has_domaine_metier` (PersonneType ↔ DomaineMetier) Fields: personne_type_oid, domaine_metier_oid
- `hls_projet_type_has_domaine_metier` (TypeProjet ↔ DomaineMetier) Fields: projet_type_oid, domaine_metier_oid