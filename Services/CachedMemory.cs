using HeliosETL.Models.v1;
using HeliosETL.Models.v2;
using HeliosETL.Repositories.v1;
using HeliosETL.Repositories.v2;
using Serilog;
using Spectre.Console;

namespace HeliosETL.Services;

public class CachedMemory
{
    private static CachedMemory _instance;
    private static Database _db = new Database();
    private static readonly ILogger _logger = LoggingService.Instance.GetLogger<CachedMemory>();
    public static StatusContext ctx = null;
    
    /*******************************************************************************************
     * V1
     ******************************************************************************************/
    /// <summary>
    /// [OLD] map avec Issue
    /// </summary>
    public HashSet<Tickets> Tickets { get; set; } = new HashSet<Tickets>();
    
    /// <summary>
    /// [OLD] map avec Journal
    /// </summary>
    public HashSet<TicketsHistorique> TicketsHistorique { get; set; } = new HashSet<TicketsHistorique>();
    
    /// <summary>
    /// [OLD] map avec Mission
    /// </summary>
    public HashSet<Categorie> Categories { get; set; } = new HashSet<Categorie>();
    
    /// <summary>
    /// [OLD] map avec Personne
    /// </summary>
    public HashSet<Contacts> Contacts { get; set; } = new HashSet<Contacts>();
    
    /// <summary>
    /// [OLD] Commandes map avec documentContractuel
    /// </summary>
    public HashSet<Commandes> Commandes { get; set; } = new HashSet<Commandes>();
    
    /*******************************************************************************************
     * V2
     ******************************************************************************************/
    /// <summary>
    /// [NEW] map avec Tickets
    /// </summary>
    public HashSet<AbstractIssue> Issues { get; set; } = new HashSet<AbstractIssue>();
    
    /// <summary>
    /// [NEW] map avec TicketsHistorique
    /// </summary>
    public HashSet<Journal> Journaux { get; set; } = new HashSet<Journal>();
    
    /// <summary>
    /// [NEW] map avec Contacts
    /// </summary>
    public HashSet<Personne> Personnes { get; set; } = new HashSet<Personne>();
    
    /// <summary>
    /// [NEW] map avec Categorie
    /// </summary>
    public HashSet<DomaineMetier> DomainesMetier { get; set; } = new HashSet<DomaineMetier>();
    
    /// <summary>
    /// [NEW] Map avec Type (Ticket) ?
    /// </summary>
    public HashSet<AbstractActivite> Activites { get; set; } = new HashSet<AbstractActivite>();
    
    /// <summary>
    /// [NEW] Map avec Categorie
    /// </summary>
    public HashSet<Mission> Missions { get; set; } = new HashSet<Mission>();
    
    /// <summary>
    /// [NEW] Map avec Priorite (Ticket)
    /// </summary>
    public HashSet<IssuePriorite> IssuePriorites { get; set; } = new HashSet<IssuePriorite>();
    
    /// <summary>
    /// [NEW] Map avec TypeMission (Mission)
    /// </summary>
    public HashSet<TypeMission> TypeMissions { get; set; } = new HashSet<TypeMission>();

    /// <summary>
    /// [NEW] Map avec Commanditaire
    /// </summary>
    public HashSet<Commanditaire> Commanditaires { get; set; } = new HashSet<Commanditaire>();

    /// <summary>
    /// [NEW] Map avec IssueOrigine
    /// </summary>
    public HashSet<IssueOrigine> IssueOrigines { get; set; } = new HashSet<IssueOrigine>();

    /// <summary>
    /// [NEW] Map avec IssueStatut
    /// </summary>
    public HashSet<IssueStatut> IssueStatuts { get; set; } = new HashSet<IssueStatut>();

    /// <summary>
    /// [NEW] Map avec JournalDetails
    /// </summary>
    public HashSet<JournalDetails> JournalDetails { get; set; } = new HashSet<JournalDetails>();

    /// <summary>
    /// [NEW] Map avec NiveauComplexite
    /// </summary>
    public HashSet<NiveauComplexite> NiveauxComplexite { get; set; } = new HashSet<NiveauComplexite>();

    /// <summary>
    /// [NEW] Map avec TypeDocument
    /// </summary>
    public HashSet<TypeDocument> TypeDocuments { get; set; } = new HashSet<TypeDocument>();

    /// <summary>
    /// [NEW] Map avec TypeProjet
    /// </summary>
    public HashSet<TypeProjet> TypeProjets { get; set; } = new HashSet<TypeProjet>();
    
    /// <summary>
    /// [NEW] Map avec Contrats
    /// </summary>
    public HashSet<DocumentContractuel> DocumentContractuels { get; set; } = new HashSet<DocumentContractuel>();
    
    public static CachedMemory Get()
    {
        if (_instance == null)
        {
            _instance = new CachedMemory();
        }
        return _instance;
    }

    public void LoadCache()
    {
        _logger.Information("Starting cache loading process...");

        try
        {
            var instance = Get();
            _db = new Database();
            _db.SetupDatabase();

            // Load V1 data (SQL Server)
            LoadV1Data(instance);

            // Load V2 data (MySQL)
            LoadV2Data(instance);

            _logger.Information("Cache loading completed successfully");
            AnsiConsole.MarkupLine("[green]Cache loaded successfully[/]");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to load cache data");
            AnsiConsole.MarkupLine("[red]Failed to load cache.[/]");
            throw;
        }
    }

    private void LoadV1Data(CachedMemory instance)
    {
        _logger.Debug("Loading V1 data from SQL Server...");
        if (_db.SqlServer == null)
        {
            _logger.Error("Failed to load V1 data: SQL Server connection is null");
            return;
        }

        try
        {
            // Load Tickets
            var ticketsRepo = new TicketsRepository(_db.SqlServer);
            instance.Tickets = ticketsRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Tickets.Count} tickets");
            AnsiConsole.MarkupLine($"[yellow](V1) Tickets chargé {instance.Tickets.Count}.[/]");

            // Load TicketsHistorique
            var ticketsHistoriqueRepo = new TicketHistoriqueRepository(_db.SqlServer);
            instance.TicketsHistorique = ticketsHistoriqueRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.TicketsHistorique.Count} tickets historique");
            AnsiConsole.MarkupLine($"[yellow](V1) TicketsHistorique chargé {instance.TicketsHistorique.Count}.[/]");

            // Load Categories
            var categorieRepo = new CategorieRepository(_db.SqlServer);
            instance.Categories = categorieRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Categories.Count} categories");
            AnsiConsole.MarkupLine($"[yellow](V1) Categories chargé {instance.Categories.Count}.[/]");

            // Load Contacts
            var contactsRepo = new ContactsRepository(_db.SqlServer);
            instance.Contacts = contactsRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Contacts.Count} contacts");
            AnsiConsole.MarkupLine($"[yellow](V1) Contacts chargé {instance.Contacts.Count}.[/]");
            
            // Load Commandes
            var commandesRepo = new CommandesRepository(_db.SqlServer);
            instance.Commandes = commandesRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Commandes.Count} commandes");
            AnsiConsole.MarkupLine($"[yellow](V1) Commandes chargé {instance.Commandes.Count}.[/]");

            _logger.Information("V1 data loading completed");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to load V1 data");
            throw;
        }
    }

    private void LoadV2Data(CachedMemory instance)
    {
        _logger.Debug("Loading V2 data from MySQL...");
        if (_db.MySqlConn == null)
        {
            _logger.Error("Failed to load V2 data: MySQL connection is null");
            return;
        }

        try
        {
            // Load Issues
            var issueRepo = new IssueRepository(_db.MySqlConn);
            instance.Issues = issueRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Issues.Count} issues");
            AnsiConsole.MarkupLine($"[yellow](V2) Issues chargé {instance.Issues.Count}.[/]");

            // Load Journaux
            var journalRepo = new JournalRepository(_db.MySqlConn);
            instance.Journaux = journalRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Journaux.Count} journaux");
            AnsiConsole.MarkupLine($"[yellow](V2) Journaux chargé {instance.Journaux.Count}.[/]");

            // Load Personnes
            var personneRepo = new PersonneRepository(_db.MySqlConn);
            instance.Personnes = personneRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Personnes.Count} personnes");
            AnsiConsole.MarkupLine($"[yellow](V2) Journaux chargé {instance.Journaux.Count}.[/]");

            // Load DomainesMetier
            var domaineMetierRepo = new DomaineMetierRepository(_db.MySqlConn);
            instance.DomainesMetier = domaineMetierRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.DomainesMetier.Count} domaines métier");
            AnsiConsole.MarkupLine($"[yellow](V2) DomainesMetier chargé {instance.DomainesMetier.Count}.[/]");

            // Load Missions
            // var missionRepo = new MissionRepository(_db.MySqlConn);
            // instance.Missions = missionRepo.GetAll().ToHashSet();
            // _logger.Debug($"Loaded {instance.Missions.Count} missions");
            // AnsiConsole.MarkupLine($"[yellow](V2) Missions chargé {instance.Missions.Count}.[/]");

            // Load IssuePriorites
            var issuePrioriteRepo = new IssuePrioriteRepository(_db.MySqlConn);
            instance.IssuePriorites = issuePrioriteRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.IssuePriorites.Count} issue priorités");
            AnsiConsole.MarkupLine($"[yellow](V2) IssuePriorites chargé {instance.IssuePriorites.Count}.[/]");

            // Load TypeMissions
            var typeMissionRepo = new TypeMissionRepository(_db.MySqlConn);
            instance.TypeMissions = typeMissionRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.TypeMissions.Count} type missions");
            AnsiConsole.MarkupLine($"[yellow](V2) TypeMissions chargé {instance.TypeMissions.Count}.[/]");

            // Load Commanditaires
            var commanditaireRepo = new CommanditaireRepository(_db.MySqlConn);
            instance.Commanditaires = commanditaireRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.Commanditaires.Count} commanditaires");
            AnsiConsole.MarkupLine($"[yellow](V2) Commanditaires chargé {instance.Commanditaires.Count}.[/]");

            // Load IssueOrigines
            var issueOrigineRepo = new IssueOrigineRepository(_db.MySqlConn);
            instance.IssueOrigines = issueOrigineRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.IssueOrigines.Count} issue origines");
            AnsiConsole.MarkupLine($"[yellow](V2) IssueOrigines chargé {instance.IssueOrigines.Count}.[/]");

            // Load IssueStatuts
            var issueStatutRepo = new IssueStatutRepository(_db.MySqlConn);
            instance.IssueStatuts = issueStatutRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.IssueStatuts.Count} issue statuts");
            AnsiConsole.MarkupLine($"[yellow](V2) IssueStatuts chargé {instance.IssueStatuts.Count}.[/]");

            // Load JournalDetails
            var journalDetailsRepo = new JournalDetailsRepository(_db.MySqlConn);
            instance.JournalDetails = journalDetailsRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.JournalDetails.Count} journal details");
            AnsiConsole.MarkupLine($"[yellow](V2) JournalDetails chargé {instance.JournalDetails.Count}.[/]");

            // Load NiveauxComplexite
            var niveauComplexiteRepo = new NiveauComplexiteRepository(_db.MySqlConn);
            instance.NiveauxComplexite = niveauComplexiteRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.NiveauxComplexite.Count} niveaux complexité");
            AnsiConsole.MarkupLine($"[yellow](V2) NiveauxComplexite chargé {instance.NiveauxComplexite.Count}.[/]");

            // Load TypeDocuments
            var typeDocumentRepo = new TypeDocumentRepository(_db.MySqlConn);
            instance.TypeDocuments = typeDocumentRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.TypeDocuments.Count} type documents");
            AnsiConsole.MarkupLine($"[yellow](V2) TypeDocuments chargé {instance.TypeDocuments.Count}.[/]");

            // Load TypeProjets
            var typeProjetRepo = new TypeProjetRepository(_db.MySqlConn);
            instance.TypeProjets = typeProjetRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.TypeProjets.Count} type projets");
            AnsiConsole.MarkupLine($"[yellow](V2) TypeProjets chargé {instance.TypeProjets.Count}.[/]");
            
            // Load DocumentContractuels
            var documentContractuelRepo = new DocumentContractuelRepository(_db.MySqlConn);
            instance.DocumentContractuels = documentContractuelRepo.GetAll().ToHashSet();
            _logger.Debug($"Loaded {instance.DocumentContractuels.Count} document contractuels");
            AnsiConsole.MarkupLine($"[yellow](V2) DocumentContractuels chargé {instance.DocumentContractuels.Count}.[/]");

            // TODO: Load Activites - ActiviteRepository not implemented yet (Activite is abstract)
            _logger.Warning("ActiviteRepository not implemented - Activites collection will remain empty (Activite is abstract, use Mission instead)");

            _logger.Information("V2 data loading completed");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to load V2 data");
            throw;
        }
    }
}