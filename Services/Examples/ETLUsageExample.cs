using HeliosETL.Models;
using HeliosETL.Models.v1;
using HeliosETL.Models.v2;
using HeliosETL.Services;
using Serilog;

namespace HeliosETL.Services.Examples;

/// <summary>
/// Example service demonstrating how to use the ETL tracking system
/// Shows best practices for relationship tracking and progress management
/// </summary>
public class ETLUsageExample
{
    private readonly ILogger _logger;
    private readonly ETLManagementService _etlManager;

    public ETLUsageExample()
    {
        _logger = LoggingService.Instance.GetLogger<ETLUsageExample>();
        _etlManager = new ETLManagementService();
    }

    /// <summary>
    /// Example: Transform Tickets to Issues with full tracking
    /// </summary>
    public async Task<bool> ExampleTicketToIssueTransformation(List<Tickets> tickets)
    {
        const string transformationName = "TicketToIssueTransformation";
        string runId = null;

        try
        {
            // 1. Start the transformation
            runId = _etlManager.StartTransformation(
                transformationName, 
                tickets.Count, 
                "1.0", 
                new Dictionary<string, object> 
                { 
                    { "source", "SQL Server" }, 
                    { "destination", "MySQL" },
                    { "batchSize", 100 }
                });

            _logger.Information($"Started {transformationName} with {tickets.Count} tickets");

            // 2. Process tickets in batches
            int processed = 0;
            int failed = 0;
            int batchSize = 100;

            for (int i = 0; i < tickets.Count; i += batchSize)
            {
                var batch = tickets.Skip(i).Take(batchSize).ToList();
                
                // Update progress - current phase
                _etlManager.UpdateProgress(runId, processed, failed, 0, 
                    "Processing", $"Processing batch {(i / batchSize) + 1}");

                foreach (var ticket in batch)
                {
                    try
                    {
                        // 3. Create relationship mapping before transformation
                        var relationshipId = _etlManager.CreateRelationship(
                            ticket.IdTickets, 
                            "Tickets", 
                            "Issue", 
                            transformationName,
                            new Dictionary<string, object>
                            {
                                { "originalTitle", ticket.Titre },
                                { "originalStatus", ticket.Status },
                                { "transformedAt", DateTime.UtcNow }
                            });

                        // 4. Perform the actual transformation
                        var issue = TransformTicketToIssue(ticket);
                        
                        // 5. Save the new entity (this would be your actual save logic)
                        var newIssueId = await SaveIssueToDatabase(issue);
                        
                        // 6. Complete the relationship mapping
                        _etlManager.CompleteRelationship(relationshipId, newIssueId);
                        
                        processed++;
                        
                        // Update progress every 10 items
                        if (processed % 10 == 0)
                        {
                            _etlManager.UpdateProgress(runId, processed, failed, 0, 
                                "Processing", $"Processed {processed}/{tickets.Count} tickets", 
                                ticket.IdTickets);
                        }
                    }
                    catch (Exception ex)
                    {
                        failed++;
                        _logger.Error(ex, $"Failed to transform ticket {ticket.IdTickets}");
                        
                        // Mark relationship as failed if it was created
                        // TODO: Get relationship ID and mark as failed
                        // _etlManager.FailRelationship(relationshipId, ex.Message);
                    }
                }
            }

            // 7. Complete the transformation
            _etlManager.UpdateProgress(runId, processed, failed, 0, "Completed", 
                $"Transformation completed: {processed} successful, {failed} failed");
            
            _etlManager.CompleteTransformation(runId, 
                $"Successfully transformed {processed} tickets to issues");

            _logger.Information($"Completed {transformationName}: {processed} successful, {failed} failed");
            return failed == 0;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"Fatal error in {transformationName}");
            
            if (!string.IsNullOrEmpty(runId))
            {
                _etlManager.FailTransformation(runId, ex.Message, ex.StackTrace);
            }
            
            return false;
        }
    }

    /// <summary>
    /// Example: Resume a failed transformation
    /// </summary>
    public async Task<bool> ExampleResumeFailedTransformation()
    {
        const string transformationName = "TicketToIssueTransformation";
        
        // 1. Check if transformation can be resumed
        if (!_etlManager.CanResumeTransformation(transformationName))
        {
            _logger.Information($"No resumable transformation found for {transformationName}");
            return false;
        }

        // 2. Get the latest progress
        var latestProgress = _etlManager.GetLatestProgress(transformationName);
        if (latestProgress == null)
        {
            _logger.Warning($"Could not find progress for {transformationName}");
            return false;
        }

        _logger.Information($"Resuming {transformationName} from run {latestProgress.RunId}");
        _logger.Information($"Last processed item ID: {latestProgress.LastProcessedItemId}");
        _logger.Information($"Progress: {latestProgress.ProcessedItems}/{latestProgress.TotalItems}");

        // 3. Get unprocessed items (this would be your actual data retrieval logic)
        var unprocessedTickets = await GetUnprocessedTickets(latestProgress.LastProcessedItemId);
        
        // 4. Update status to retrying
        _etlManager.UpdateProgress(latestProgress.RunId, latestProgress.ProcessedItems, 
            latestProgress.FailedItems, latestProgress.SkippedItems, "Resuming", 
            $"Resuming transformation with {unprocessedTickets.Count} remaining items");

        // 5. Continue processing from where we left off
        // (Similar logic to the main transformation above)
        
        return true;
    }

    /// <summary>
    /// Example: Query relationship mappings
    /// </summary>
    public void ExampleQueryRelationships()
    {
        // Get new Issue ID for a specific Ticket
        var ticketId = 12345;
        var issueId = _etlManager.GetNewEntityId(ticketId, "Tickets");
        if (issueId.HasValue)
        {
            _logger.Information($"Ticket {ticketId} was transformed to Issue {issueId.Value}");
        }

        // Get all relationships for a transformation
        var relationships = _etlManager.GetTransformationRelationships("TicketToIssueTransformation");
        _logger.Information($"Found {relationships.Count()} relationships for TicketToIssueTransformation");

        // Get relationship statistics
        var stats = _etlManager.GetRelationshipStatistics();
        foreach (var stat in stats)
        {
            _logger.Information($"Relationships in {stat.Key} state: {stat.Value}");
        }
    }

    /// <summary>
    /// Example: Get ETL dashboard information
    /// </summary>
    public Dictionary<string, object> GetETLDashboardData()
    {
        var dashboardData = new Dictionary<string, object>();
        
        // Get overall statistics
        var stats = _etlManager.GetETLStatistics();
        dashboardData["Statistics"] = stats;
        
        // Get resumable transformations
        var resumable = _etlManager.GetResumableTransformations().ToList();
        dashboardData["ResumableTransformations"] = resumable.Select(r => new
        {
            r.TransformationName,
            r.RunId,
            r.Status,
            r.StartedAt,
            r.ProcessedItems,
            r.TotalItems,
            ProgressPercentage = r.TotalItems > 0 ? (r.ProcessedItems * 100.0 / r.TotalItems) : 0
        });
        
        // Get recent transformations
        var recentTransformations = new[] { "TicketToIssueTransformation", "TicketHistoryToJournalTransformation" }
            .Select(name => _etlManager.GetLatestProgress(name))
            .Where(p => p != null)
            .ToList();
        
        dashboardData["RecentTransformations"] = recentTransformations;
        
        return dashboardData;
    }

    #region Helper Methods (These would be implemented in your actual transformers)

    private AbstractIssue TransformTicketToIssue(Tickets ticket)
    {
        // TODO: Implement actual transformation logic
        return new AbstractIssue
        {
            Code = ticket.CtNum,
            Sujet = ticket.Titre,
            Description = ticket.Description,
            DateCreation = ticket.DateCreation,
            // ... other mappings
        };
    }

    private async Task<long> SaveIssueToDatabase(AbstractIssue issue)
    {
        // TODO: Implement actual database save logic
        // This would use your IssueRepository
        await Task.Delay(10); // Simulate async operation
        return new Random().Next(1000, 9999); // Simulate new ID
    }

    private async Task<List<Tickets>> GetUnprocessedTickets(long? lastProcessedId)
    {
        // TODO: Implement actual data retrieval logic
        // This would query your TicketsRepository for items with ID > lastProcessedId
        await Task.Delay(100); // Simulate async operation
        return new List<Tickets>(); // Return actual unprocessed tickets
    }

    #endregion
}
