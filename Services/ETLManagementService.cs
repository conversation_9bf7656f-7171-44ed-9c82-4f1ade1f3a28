using HeliosETL.Models;
using HeliosETL.Repositories;
using Microsoft.Data.Sqlite;
using Serilog;
using System.Text.Json;

namespace HeliosETL.Services;

/// <summary>
/// Service for managing ETL operations, relationships, and progress tracking
/// Provides high-level operations for ETL processes
/// </summary>
public class ETLManagementService
{
    private readonly ILogger _logger;
    private readonly Database _database;
    private readonly ETLRelationshipRepository _relationshipRepository;
    private readonly ETLProgressStateRepository _progressRepository;
    private readonly ETLHistoryRepository _historyRepository;

    public ETLManagementService()
    {
        _logger = LoggingService.Instance.GetLogger<ETLManagementService>();
        _database = new Database();

        // Don't call SetupDatabase here to avoid circular dependency
        // The database should already be set up by the caller

        if (_database.Sqlite == null)
        {
            // Create SQLite connection directly if not available
            var connectionString = _database.GetSqliteConnectionString();
            _database.Sqlite = new SqliteConnection(connectionString);
        }

        _relationshipRepository = new ETLRelationshipRepository(_database.Sqlite);
        _progressRepository = new ETLProgressStateRepository(_database.Sqlite);
        _historyRepository = new ETLHistoryRepository(_database.Sqlite);
    }

    #region Progress State Management

    /// <summary>
    /// Start a new ETL transformation run
    /// </summary>
    public string StartTransformation(string transformationName, long totalItems, 
        string transformationVersion = "1.0", Dictionary<string, object> configuration = null)
    {
        var runId = Guid.NewGuid().ToString();
        
        var progressState = new ETLProgressState
        {
            TransformationName = transformationName,
            RunId = runId,
            Status = ETLProgressStatus.InProgress,
            StartedAt = DateTime.UtcNow,
            TotalItems = totalItems,
            TransformationVersion = transformationVersion,
            Configuration = configuration != null ? JsonSerializer.Serialize(configuration) : null,
            CurrentPhase = "Starting"
        };

        _progressRepository.Add(progressState);
        _logger.Information($"Started transformation {transformationName} with run ID {runId}");
        
        return runId;
    }

    /// <summary>
    /// Update transformation progress
    /// </summary>
    public void UpdateProgress(string runId, long processedItems, long failedItems = 0, 
        long skippedItems = 0, string currentPhase = null, string progressMessage = null, 
        long? lastProcessedItemId = null)
    {
        _progressRepository.UpdateProgress(runId, processedItems, failedItems, skippedItems, 
            currentPhase, progressMessage, lastProcessedItemId);
        
        _logger.Debug($"Updated progress for run {runId}: {processedItems} processed, {failedItems} failed, {skippedItems} skipped");
    }

    /// <summary>
    /// Complete a transformation successfully
    /// </summary>
    public void CompleteTransformation(string runId, string completionMessage = null)
    {
        _progressRepository.UpdateStatus(runId, ETLProgressStatus.Completed);
        
        if (!string.IsNullOrEmpty(completionMessage))
        {
            _progressRepository.UpdateProgress(runId, 0, 0, 0, "Completed", completionMessage);
        }
        
        _logger.Information($"Completed transformation run {runId}");
    }

    /// <summary>
    /// Mark a transformation as failed
    /// </summary>
    public void FailTransformation(string runId, string errorMessage, string stackTrace = null)
    {
        _progressRepository.UpdateStatus(runId, ETLProgressStatus.Failed, errorMessage, stackTrace);
        _logger.Error($"Failed transformation run {runId}: {errorMessage}");
    }

    /// <summary>
    /// Pause a transformation (can be resumed later)
    /// </summary>
    public void PauseTransformation(string runId, string reason = null)
    {
        _progressRepository.UpdateStatus(runId, ETLProgressStatus.Paused);
        if (!string.IsNullOrEmpty(reason))
        {
            _progressRepository.UpdateProgress(runId, 0, 0, 0, "Paused", reason);
        }
        _logger.Information($"Paused transformation run {runId}");
    }

    /// <summary>
    /// Get resumable transformations
    /// </summary>
    public IEnumerable<ETLProgressState> GetResumableTransformations()
    {
        return _progressRepository.GetResumableTransformations();
    }

    /// <summary>
    /// Get the latest progress for a transformation
    /// </summary>
    public ETLProgressState GetLatestProgress(string transformationName)
    {
        return _progressRepository.GetLatestByTransformation(transformationName);
    }

    #endregion

    #region Relationship Management

    /// <summary>
    /// Create a new entity relationship mapping
    /// </summary>
    public long CreateRelationship(long oldEntityId, string oldEntityType, string newEntityType, 
        string transformationName, Dictionary<string, object> metadata = null)
    {
        var relationship = new ETLRelationship
        {
            OldEntityId = oldEntityId,
            OldEntityType = oldEntityType,
            NewEntityType = newEntityType,
            State = ETLRelationshipState.Pending,
            TransformationName = transformationName,
            Metadata = metadata != null ? JsonSerializer.Serialize(metadata) : null
        };

        _relationshipRepository.Add(relationship);
        _logger.Debug($"Created relationship mapping for {oldEntityType} ID {oldEntityId}");
        
        return relationship.Id;
    }

    /// <summary>
    /// Complete a relationship mapping with the new entity ID
    /// </summary>
    public void CompleteRelationship(long relationshipId, long newEntityId)
    {
        var relationship = _relationshipRepository.GetById((int)relationshipId);
        if (relationship != null)
        {
            relationship.NewEntityId = newEntityId;
            relationship.State = ETLRelationshipState.Completed;
            _relationshipRepository.Update(relationship);
            
            _logger.Debug($"Completed relationship mapping: {relationship.OldEntityType} {relationship.OldEntityId} -> {relationship.NewEntityType} {newEntityId}");
        }
    }

    /// <summary>
    /// Mark a relationship as failed
    /// </summary>
    public void FailRelationship(long relationshipId, string errorMessage)
    {
        _relationshipRepository.UpdateState(relationshipId, ETLRelationshipState.Failed, errorMessage);
        _logger.Warning($"Failed relationship mapping {relationshipId}: {errorMessage}");
    }

    /// <summary>
    /// Get the new entity ID for an old entity
    /// </summary>
    public long? GetNewEntityId(long oldEntityId, string oldEntityType)
    {
        var relationship = _relationshipRepository.GetByOldEntity(oldEntityId, oldEntityType);
        return relationship?.State == ETLRelationshipState.Completed ? relationship.NewEntityId : null;
    }

    /// <summary>
    /// Get the old entity ID for a new entity
    /// </summary>
    public long? GetOldEntityId(long newEntityId, string newEntityType)
    {
        var relationship = _relationshipRepository.GetByNewEntity(newEntityId, newEntityType);
        return relationship?.State == ETLRelationshipState.Completed ? relationship.OldEntityId : null;
    }

    /// <summary>
    /// Get all relationships for a transformation
    /// </summary>
    public IEnumerable<ETLRelationship> GetTransformationRelationships(string transformationName)
    {
        return _relationshipRepository.GetByTransformation(transformationName);
    }

    /// <summary>
    /// Get relationship statistics
    /// </summary>
    public Dictionary<ETLRelationshipState, int> GetRelationshipStatistics()
    {
        return _relationshipRepository.GetStateCounts();
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// Check if a transformation can be resumed
    /// </summary>
    public bool CanResumeTransformation(string transformationName)
    {
        var latest = GetLatestProgress(transformationName);
        return latest != null && latest.CanResume && 
               (latest.Status == ETLProgressStatus.Failed || latest.Status == ETLProgressStatus.Paused);
    }

    /// <summary>
    /// Get comprehensive ETL statistics
    /// </summary>
    public Dictionary<string, object> GetETLStatistics()
    {
        var stats = new Dictionary<string, object>();
        
        // Progress statistics
        var progressStats = _progressRepository.GetSummaryStatistics();
        foreach (var stat in progressStats)
        {
            stats[$"Progress_{stat.Key}"] = stat.Value;
        }
        
        // Relationship statistics
        var relationshipStats = GetRelationshipStatistics();
        foreach (var stat in relationshipStats)
        {
            stats[$"Relationship_{stat.Key}"] = stat.Value;
        }
        
        return stats;
    }

    /// <summary>
    /// Clean up old completed transformations (older than specified days)
    /// </summary>
    public int CleanupOldTransformations(int daysOld = 30)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        var allProgress = _progressRepository.GetAll();
        
        var toDelete = allProgress.Where(p => 
            p.Status == ETLProgressStatus.Completed && 
            p.CompletedAt.HasValue && 
            p.CompletedAt.Value < cutoffDate).ToList();
        
        foreach (var progress in toDelete)
        {
            _progressRepository.Delete(progress);
        }
        
        _logger.Information($"Cleaned up {toDelete.Count} old transformation records");
        return toDelete.Count;
    }

    /// <summary>
    /// Validate database integrity
    /// </summary>
    public bool ValidateIntegrity()
    {
        try
        {
            // Validate by checking if we can query the main tables
            var tables = new[] { "ETLHistory", "ETLRelationship", "ETLProgressState" };

            using var connection = new SqliteConnection(_database.GetSqliteConnectionString());
            connection.Open();

            foreach (var table in tables)
            {
                using var command = new SqliteCommand(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=@tableName;",
                    connection);
                command.Parameters.AddWithValue("@tableName", table);

                var result = command.ExecuteScalar();
                if (result == null)
                {
                    _logger.Warning($"Table {table} does not exist");
                    return false;
                }
            }

            _logger.Debug("Database integrity validation successful");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to validate database integrity");
            return false;
        }
    }

    #endregion
}
