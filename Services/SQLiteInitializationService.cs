using Microsoft.Data.Sqlite;
using HeliosETL.Services;
using Serilog;
using System.Text;

namespace HeliosETL.Services;

/// <summary>
/// Service responsible for initializing and managing the SQLite database schema
/// Creates tables for ETL tracking, relationships, and progress state
/// </summary>
public class SQLiteInitializationService
{
    private readonly ILogger _logger;
    private readonly Database _database;

    public SQLiteInitializationService()
    {
        _logger = LoggingService.Instance.GetLogger<SQLiteInitializationService>();
        _database = new Database();
    }

    /// <summary>
    /// Initialize the SQLite database with all required tables
    /// </summary>
    /// <returns>True if initialization was successful, false otherwise</returns>
    public bool InitializeDatabase()
    {
        try
        {
            _logger.Information("Starting SQLite database initialization");
            
            if (!_database.SetupDatabase())
            {
                _logger.Error("Failed to setup database connections");
                return false;
            }

            if (_database.Sqlite == null)
            {
                _logger.Error("SQLite connection is null");
                return false;
            }

            // Create all tables
            CreateETLHistoryTable();
            CreateETLRelationshipTable();
            CreateETLProgressStateTable();
            CreateIndexes();

            _logger.Information("SQLite database initialization completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to initialize SQLite database");
            return false;
        }
    }

    /// <summary>
    /// Create the ETLHistory table (enhanced version)
    /// </summary>
    private void CreateETLHistoryTable()
    {
        var sql = @"
            CREATE TABLE IF NOT EXISTS ETLHistory (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                oldId INTEGER NOT NULL,
                newId INTEGER,
                tableName TEXT NOT NULL,
                action TEXT NOT NULL,
                errorMessage TEXT,
                state TEXT NOT NULL,
                date DATETIME NOT NULL,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            );";

        ExecuteNonQuery(sql, "ETLHistory");
    }

    /// <summary>
    /// Create the ETLRelationship table for tracking entity mappings
    /// </summary>
    private void CreateETLRelationshipTable()
    {
        var sql = @"
            CREATE TABLE IF NOT EXISTS ETLRelationship (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                OldEntityId INTEGER NOT NULL,
                NewEntityId INTEGER,
                OldEntityType TEXT NOT NULL,
                NewEntityType TEXT NOT NULL,
                State TEXT NOT NULL DEFAULT 'Pending',
                CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                ErrorMessage TEXT,
                Metadata TEXT,
                TransformationName TEXT NOT NULL,
                TransformationVersion TEXT DEFAULT '1.0'
            );";

        ExecuteNonQuery(sql, "ETLRelationship");
    }

    /// <summary>
    /// Create the ETLProgressState table for tracking transformation progress
    /// </summary>
    private void CreateETLProgressStateTable()
    {
        var sql = @"
            CREATE TABLE IF NOT EXISTS ETLProgressState (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                TransformationName TEXT NOT NULL,
                RunId TEXT NOT NULL UNIQUE,
                Status TEXT NOT NULL DEFAULT 'NotStarted',
                StartedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                CompletedAt DATETIME,
                LastUpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                TotalItems INTEGER DEFAULT 0,
                ProcessedItems INTEGER DEFAULT 0,
                FailedItems INTEGER DEFAULT 0,
                SkippedItems INTEGER DEFAULT 0,
                CurrentPhase TEXT,
                ProgressMessage TEXT,
                ErrorMessage TEXT,
                ErrorStackTrace TEXT,
                Configuration TEXT,
                Metadata TEXT,
                TransformationVersion TEXT DEFAULT '1.0',
                LastProcessedItemId INTEGER,
                CanResume BOOLEAN DEFAULT 1,
                RetryCount INTEGER DEFAULT 0,
                MaxRetries INTEGER DEFAULT 3
            );";

        ExecuteNonQuery(sql, "ETLProgressState");
    }

    /// <summary>
    /// Create indexes for better query performance
    /// </summary>
    private void CreateIndexes()
    {
        var indexes = new[]
        {
            // ETLHistory indexes
            "CREATE INDEX IF NOT EXISTS idx_etlhistory_oldid ON ETLHistory(oldId);",
            "CREATE INDEX IF NOT EXISTS idx_etlhistory_tablename ON ETLHistory(tableName);",
            "CREATE INDEX IF NOT EXISTS idx_etlhistory_state ON ETLHistory(state);",
            "CREATE INDEX IF NOT EXISTS idx_etlhistory_date ON ETLHistory(date);",
            
            // ETLRelationship indexes
            "CREATE INDEX IF NOT EXISTS idx_etlrel_oldentity ON ETLRelationship(OldEntityId, OldEntityType);",
            "CREATE INDEX IF NOT EXISTS idx_etlrel_newentity ON ETLRelationship(NewEntityId, NewEntityType);",
            "CREATE INDEX IF NOT EXISTS idx_etlrel_state ON ETLRelationship(State);",
            "CREATE INDEX IF NOT EXISTS idx_etlrel_transformation ON ETLRelationship(TransformationName);",
            "CREATE INDEX IF NOT EXISTS idx_etlrel_created ON ETLRelationship(CreatedAt);",
            
            // ETLProgressState indexes
            "CREATE INDEX IF NOT EXISTS idx_etlprogress_transformation ON ETLProgressState(TransformationName);",
            "CREATE INDEX IF NOT EXISTS idx_etlprogress_runid ON ETLProgressState(RunId);",
            "CREATE INDEX IF NOT EXISTS idx_etlprogress_status ON ETLProgressState(Status);",
            "CREATE INDEX IF NOT EXISTS idx_etlprogress_started ON ETLProgressState(StartedAt);",
            "CREATE INDEX IF NOT EXISTS idx_etlprogress_canresume ON ETLProgressState(CanResume);"
        };

        foreach (var indexSql in indexes)
        {
            ExecuteNonQuery(indexSql, "Index");
        }
    }

    /// <summary>
    /// Execute a non-query SQL command
    /// </summary>
    private void ExecuteNonQuery(string sql, string description)
    {
        try
        {
            using var connection = new SqliteConnection(_database.GetSqliteConnectionString());
            connection.Open();
            using var command = new SqliteCommand(sql, connection);
            command.ExecuteNonQuery();
            _logger.Debug($"Successfully created/updated {description}");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"Failed to create/update {description}");
            throw;
        }
    }

    /// <summary>
    /// Check if the database tables exist and are properly configured
    /// </summary>
    /// <returns>True if all tables exist, false otherwise</returns>
    public bool ValidateSchema()
    {
        try
        {
            var tables = new[] { "ETLHistory", "ETLRelationship", "ETLProgressState" };
            
            using var connection = new SqliteConnection(_database.GetSqliteConnectionString());
            connection.Open();
            
            foreach (var table in tables)
            {
                using var command = new SqliteCommand(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=@tableName;", 
                    connection);
                command.Parameters.AddWithValue("@tableName", table);
                
                var result = command.ExecuteScalar();
                if (result == null)
                {
                    _logger.Warning($"Table {table} does not exist");
                    return false;
                }
            }
            
            _logger.Information("Database schema validation successful");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to validate database schema");
            return false;
        }
    }

    /// <summary>
    /// Get database statistics and information
    /// </summary>
    /// <returns>Dictionary containing database statistics</returns>
    public Dictionary<string, object> GetDatabaseInfo()
    {
        var info = new Dictionary<string, object>();
        
        try
        {
            using var connection = new SqliteConnection(_database.GetSqliteConnectionString());
            connection.Open();
            
            // Get table counts
            var tables = new[] { "ETLHistory", "ETLRelationship", "ETLProgressState" };
            foreach (var table in tables)
            {
                using var command = new SqliteCommand($"SELECT COUNT(*) FROM {table};", connection);
                var count = command.ExecuteScalar();
                info[$"{table}_Count"] = count ?? 0;
            }
            
            // Get database file size
            using var sizeCommand = new SqliteCommand("PRAGMA page_count; PRAGMA page_size;", connection);
            // TODO: Implement database size calculation
            
            _logger.Debug("Retrieved database information successfully");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to retrieve database information");
        }
        
        return info;
    }
}
