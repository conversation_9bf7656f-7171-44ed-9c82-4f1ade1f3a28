using System.Data;
using HeliosETL.Exceptions;
using Microsoft.Data.SqlClient;
using Microsoft.Data.Sqlite;
using MySql.Data.MySqlClient;
using HeliosETL.Models.Configuration;
using Serilog;
using Spectre.Console;

namespace HeliosETL.Services;

public class Database
{
    public MySqlConnection? MySqlConn { get; set; }
    public SqlConnection? SqlServer { get; set; }
    public SqliteConnection? Sqlite { get; set; }
    private DatabaseConfig? _databaseConfig;
    private readonly ILogger _logger;

    public Database()
    {
        _logger = LoggingService.Instance.GetLogger<Database>();
        _logger.Debug("Initializing Database service");
    }

    public bool SetupDatabase()
    {
        try
        {
            _logger.Information("Setting up database connections");
            _databaseConfig = ConfigurationService.Instance.GetDatabaseConfig();
            this.MySqlConn = new MySqlConnection(this.GetHeliosMySqlConnectionString());
            this.SqlServer = new SqlConnection(this.GetHeliosSqlServerConnectionString());
            this.Sqlite = new SqliteConnection(this.GetSqliteConnectionString());

            // Initialize SQLite database schema
            var sqliteInit = new SQLiteInitializationService();
            if (!sqliteInit.InitializeDatabase())
            {
                _logger.Error("Failed to initialize SQLite database schema");
                return false;
            }

            _logger.Information("Database connections and SQLite schema setup successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to setup database connections");
            return false;
        }
    }

    public string GetHeliosSqlServerConnectionString()
    {
        if(_databaseConfig == null)
        {
            throw new DatabaseException("Database configuration is null");
        }
        
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder();
        builder.DataSource = _databaseConfig.SqlServer.DataSource;
        builder.InitialCatalog = _databaseConfig.SqlServer.InitialCatalog;
        builder.UserID = _databaseConfig.SqlServer.UserID;
        builder.Password = _databaseConfig.SqlServer.Password;
        builder.TrustServerCertificate = true;
        return builder.ConnectionString;
    }
    
    public string GetHeliosMySqlConnectionString()
    {
        if(_databaseConfig == null)
        {
            throw new DatabaseException("Database configuration is null");
        }
        
        MySqlConnectionStringBuilder builder = new MySqlConnectionStringBuilder();
        builder.Server = _databaseConfig.MySQL.Server;
        builder.Database = _databaseConfig.MySQL.Database;
        builder.Port = _databaseConfig.MySQL.Port;
        builder.UserID = _databaseConfig.MySQL.UserID;
        builder.Password = _databaseConfig.MySQL.Password;
        return builder.ConnectionString;
    }
    
    public string GetSqliteConnectionString()
    {
        if(_databaseConfig == null)
        {
            throw new DatabaseException("Database configuration is null");
        }
        
        SqliteConnectionStringBuilder builder = new SqliteConnectionStringBuilder();
        builder.DataSource = "ETL.db";
        return builder.ConnectionString;
    }

    /// <summary>
    /// Verification que la connexion avec la base SQL de donnees fonctionne correctement
    /// </summary>
    public bool testConnexions()
    {
        if (this.MySqlConn == null || this.SqlServer == null)
        {
            throw new DatabaseException("Database connections are not initialized");
        }

        bool success = true;
        
        _logger.Information("Testing database connections");
        try
        {
            this.MySqlConn.Open();
            _logger.Information("MySql Database connection test successful");
            AnsiConsole.MarkupLine("[green]MySql Database connection test successful[/]");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "MySql Database connection test failed");
            AnsiConsole.MarkupLine("[red]MySql Database connection test failed[/]");
            success = false;
        }
        finally
        {
            if (this.MySqlConn.State == ConnectionState.Open)
            {
                this.MySqlConn.Close();
            }
        }

        try
        {
            this.SqlServer.Open();
            _logger.Information("SQLServer Database connection test successful");
            AnsiConsole.MarkupLine("[green]SQLServer Database connection test successful[/]");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "SQLServer Database connection test failed");
            AnsiConsole.MarkupLine("[red]SQLServer Database connection test failed[/]");
            success = false;
        }
        finally
        {
            if (this.SqlServer.State == ConnectionState.Open)
            {
                this.SqlServer.Close();
            }
        }

        return success;
    }
}