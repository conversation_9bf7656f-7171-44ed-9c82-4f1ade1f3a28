-- HeliosETL SQLite Database Schema
-- This file contains the complete schema for the ETL tracking database
-- Used for storing relationships between old and new system entities and tracking transformation progress

-- =============================================================================
-- ETL History Table (Legacy - Enhanced)
-- Tracks individual entity transformations for backward compatibility
-- =============================================================================
CREATE TABLE IF NOT EXISTS ETLHistory (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    oldId INTEGER NOT NULL,
    newId INTEGER,
    tableName TEXT NOT NULL,
    action TEXT NOT NULL,
    errorMessage TEXT,
    state TEXT NOT NULL,
    date DATETIME NOT NULL,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================================
-- ETL Relationship Table
-- Tracks mappings between old system entities and new system entities
-- =============================================================================
CREATE TABLE IF NOT EXISTS ETLRelationship (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    OldEntityId INTEGER NOT NULL,                    -- ID in the old system (v1)
    NewEntityId INTEGER,                             -- ID in the new system (v2)
    OldEntityType TEXT NOT NULL,                     -- Type: "Tickets", "TicketsHistorique", "Categorie"
    NewEntityType TEXT NOT NULL,                     -- Type: "Issue", "Journal", "Mission"
    State TEXT NOT NULL DEFAULT 'Pending',           -- Pending, InProgress, Completed, Failed, Skipped, Retry
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    ErrorMessage TEXT,                               -- Error details if failed
    Metadata TEXT,                                   -- JSON metadata about the transformation
    TransformationName TEXT NOT NULL,               -- Name of the transformation process
    TransformationVersion TEXT DEFAULT '1.0'        -- Version of the transformation
);

-- =============================================================================
-- ETL Progress State Table
-- Tracks the overall progress of ETL transformation processes
-- Enables stopping/starting ETL and recovering from failures
-- =============================================================================
CREATE TABLE IF NOT EXISTS ETLProgressState (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TransformationName TEXT NOT NULL,               -- e.g., "TicketToIssueTransformation"
    RunId TEXT NOT NULL UNIQUE,                     -- Unique identifier for this run
    Status TEXT NOT NULL DEFAULT 'NotStarted',      -- NotStarted, InProgress, Completed, Failed, Cancelled, Paused, Retrying, Validating, CompletedWithWarnings
    StartedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    CompletedAt DATETIME,
    LastUpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    TotalItems INTEGER DEFAULT 0,                   -- Total number of items to process
    ProcessedItems INTEGER DEFAULT 0,               -- Successfully processed items
    FailedItems INTEGER DEFAULT 0,                  -- Failed items
    SkippedItems INTEGER DEFAULT 0,                 -- Skipped items
    CurrentPhase TEXT,                              -- Current phase: Extract, Transform, Load, etc.
    ProgressMessage TEXT,                           -- Detailed progress message
    ErrorMessage TEXT,                              -- Error message if failed
    ErrorStackTrace TEXT,                           -- Stack trace if failed
    Configuration TEXT,                             -- JSON configuration for this run
    Metadata TEXT,                                  -- JSON metadata about the run
    TransformationVersion TEXT DEFAULT '1.0',       -- Version of the transformation
    LastProcessedItemId INTEGER,                    -- ID of last processed item (for resuming)
    CanResume BOOLEAN DEFAULT 1,                    -- Whether this transformation can be resumed
    RetryCount INTEGER DEFAULT 0,                   -- Number of retry attempts
    MaxRetries INTEGER DEFAULT 3                    -- Maximum retries allowed
);

-- =============================================================================
-- Indexes for Performance
-- =============================================================================

-- ETLHistory indexes
CREATE INDEX IF NOT EXISTS idx_etlhistory_oldid ON ETLHistory(oldId);
CREATE INDEX IF NOT EXISTS idx_etlhistory_tablename ON ETLHistory(tableName);
CREATE INDEX IF NOT EXISTS idx_etlhistory_state ON ETLHistory(state);
CREATE INDEX IF NOT EXISTS idx_etlhistory_date ON ETLHistory(date);

-- ETLRelationship indexes
CREATE INDEX IF NOT EXISTS idx_etlrel_oldentity ON ETLRelationship(OldEntityId, OldEntityType);
CREATE INDEX IF NOT EXISTS idx_etlrel_newentity ON ETLRelationship(NewEntityId, NewEntityType);
CREATE INDEX IF NOT EXISTS idx_etlrel_state ON ETLRelationship(State);
CREATE INDEX IF NOT EXISTS idx_etlrel_transformation ON ETLRelationship(TransformationName);
CREATE INDEX IF NOT EXISTS idx_etlrel_created ON ETLRelationship(CreatedAt);

-- ETLProgressState indexes
CREATE INDEX IF NOT EXISTS idx_etlprogress_transformation ON ETLProgressState(TransformationName);
CREATE INDEX IF NOT EXISTS idx_etlprogress_runid ON ETLProgressState(RunId);
CREATE INDEX IF NOT EXISTS idx_etlprogress_status ON ETLProgressState(Status);
CREATE INDEX IF NOT EXISTS idx_etlprogress_started ON ETLProgressState(StartedAt);
CREATE INDEX IF NOT EXISTS idx_etlprogress_canresume ON ETLProgressState(CanResume);

-- =============================================================================
-- Example Usage Queries
-- =============================================================================

-- Get the new Issue ID for a specific Ticket ID
-- SELECT NewEntityId FROM ETLRelationship 
-- WHERE OldEntityId = ? AND OldEntityType = 'Tickets' AND NewEntityType = 'Issue' AND State = 'Completed';

-- Get all failed transformations that can be resumed
-- SELECT * FROM ETLProgressState 
-- WHERE Status = 'Failed' AND CanResume = 1 
-- ORDER BY StartedAt DESC;

-- Get transformation progress summary
-- SELECT 
--     TransformationName,
--     Status,
--     ProcessedItems,
--     TotalItems,
--     (ProcessedItems * 100.0 / TotalItems) as ProgressPercentage,
--     StartedAt,
--     LastUpdatedAt
-- FROM ETLProgressState 
-- WHERE Status = 'InProgress';

-- Get relationship mapping statistics
-- SELECT 
--     OldEntityType,
--     NewEntityType,
--     State,
--     COUNT(*) as Count
-- FROM ETLRelationship 
-- GROUP BY OldEntityType, NewEntityType, State;

-- Find orphaned relationships (old entities without new mappings)
-- SELECT DISTINCT OldEntityId, OldEntityType 
-- FROM ETLRelationship 
-- WHERE State != 'Completed' AND State != 'Skipped';

-- =============================================================================
-- Data Integrity Notes
-- =============================================================================

-- 1. ETLRelationship.OldEntityId + OldEntityType should be unique for completed mappings
-- 2. ETLRelationship.NewEntityId + NewEntityType should be unique for completed mappings
-- 3. ETLProgressState.RunId must be unique across all transformations
-- 4. When a transformation is marked as Completed, all its relationships should be Completed or Skipped
-- 5. LastProcessedItemId should be used to resume transformations from the correct point

-- =============================================================================
-- Maintenance Queries
-- =============================================================================

-- Clean up old completed transformations (older than 30 days)
-- DELETE FROM ETLProgressState 
-- WHERE Status = 'Completed' AND CompletedAt < datetime('now', '-30 days');

-- Reset failed transformation for retry
-- UPDATE ETLProgressState 
-- SET Status = 'NotStarted', RetryCount = RetryCount + 1, ErrorMessage = NULL, ErrorStackTrace = NULL
-- WHERE RunId = ? AND RetryCount < MaxRetries;

-- Get database size information
-- SELECT 
--     (SELECT COUNT(*) FROM ETLHistory) as ETLHistory_Count,
--     (SELECT COUNT(*) FROM ETLRelationship) as ETLRelationship_Count,
--     (SELECT COUNT(*) FROM ETLProgressState) as ETLProgressState_Count;
