using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models;
using Microsoft.Data.Sqlite;

namespace HeliosETL.Repositories
{
    /// <summary>
    /// Repository for managing ETL relationship mappings between old and new system entities
    /// </summary>
    public class ETLRelationshipRepository : Repository<ETLRelationship>
    {
        public ETLRelationshipRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<ETLRelationship> GetAll()
        {
            var relationships = new List<ETLRelationship>();
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLRelationship ORDER BY CreatedAt DESC", (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        relationships.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return relationships;
        }

        public override ETLRelationship GetById(int id)
        {
            ETLRelationship relationship = null;
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLRelationship WHERE Id = @id", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        relationship = MapFromReader(reader);
                    }
                }
            }
            CloseConnection();
            return relationship;
        }

        /// <summary>
        /// Get relationship by old entity ID and type
        /// </summary>
        public ETLRelationship GetByOldEntity(long oldEntityId, string oldEntityType)
        {
            ETLRelationship relationship = null;
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLRelationship WHERE OldEntityId = @oldEntityId AND OldEntityType = @oldEntityType", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@oldEntityId", oldEntityId);
                command.Parameters.AddWithValue("@oldEntityType", oldEntityType);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        relationship = MapFromReader(reader);
                    }
                }
            }
            CloseConnection();
            return relationship;
        }

        /// <summary>
        /// Get relationship by new entity ID and type
        /// </summary>
        public ETLRelationship GetByNewEntity(long newEntityId, string newEntityType)
        {
            ETLRelationship relationship = null;
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLRelationship WHERE NewEntityId = @newEntityId AND NewEntityType = @newEntityType", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@newEntityId", newEntityId);
                command.Parameters.AddWithValue("@newEntityType", newEntityType);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        relationship = MapFromReader(reader);
                    }
                }
            }
            CloseConnection();
            return relationship;
        }

        /// <summary>
        /// Get all relationships for a specific transformation
        /// </summary>
        public IEnumerable<ETLRelationship> GetByTransformation(string transformationName)
        {
            var relationships = new List<ETLRelationship>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLRelationship WHERE TransformationName = @transformationName ORDER BY CreatedAt", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@transformationName", transformationName);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        relationships.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return relationships;
        }

        /// <summary>
        /// Get relationships by state
        /// </summary>
        public IEnumerable<ETLRelationship> GetByState(ETLRelationshipState state)
        {
            var relationships = new List<ETLRelationship>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLRelationship WHERE State = @state ORDER BY CreatedAt", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@state", state.ToString());
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        relationships.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return relationships;
        }

        /// <summary>
        /// Get relationships by old entity type
        /// </summary>
        public IEnumerable<ETLRelationship> GetByOldEntityType(string oldEntityType)
        {
            var relationships = new List<ETLRelationship>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLRelationship WHERE OldEntityType = @oldEntityType ORDER BY OldEntityId", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@oldEntityType", oldEntityType);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        relationships.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return relationships;
        }

        public override void Add(ETLRelationship entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand(@"
                INSERT INTO ETLRelationship 
                (OldEntityId, NewEntityId, OldEntityType, NewEntityType, State, CreatedAt, UpdatedAt, 
                 ErrorMessage, Metadata, TransformationName, TransformationVersion) 
                VALUES 
                (@OldEntityId, @NewEntityId, @OldEntityType, @NewEntityType, @State, @CreatedAt, @UpdatedAt, 
                 @ErrorMessage, @Metadata, @TransformationName, @TransformationVersion)", 
                (SqliteConnection)_connection))
            {
                AddParameters(command, entity);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(ETLRelationship entity)
        {
            entity.UpdatedAt = DateTime.UtcNow;
            OpenConnection();
            using (var command = new SqliteCommand(@"
                UPDATE ETLRelationship SET 
                    NewEntityId = @NewEntityId, 
                    State = @State, 
                    UpdatedAt = @UpdatedAt, 
                    ErrorMessage = @ErrorMessage, 
                    Metadata = @Metadata 
                WHERE Id = @Id", 
                (SqliteConnection)_connection))
            {
                AddParameters(command, entity);
                command.Parameters.AddWithValue("@Id", entity.Id);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(ETLRelationship entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand("DELETE FROM ETLRelationship WHERE Id = @Id", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@Id", entity.Id);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        /// <summary>
        /// Update the state of a relationship
        /// </summary>
        public void UpdateState(long id, ETLRelationshipState state, string errorMessage = null)
        {
            OpenConnection();
            using (var command = new SqliteCommand(@"
                UPDATE ETLRelationship SET 
                    State = @State, 
                    UpdatedAt = @UpdatedAt, 
                    ErrorMessage = @ErrorMessage 
                WHERE Id = @Id", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@State", state.ToString());
                command.Parameters.AddWithValue("@UpdatedAt", DateTime.UtcNow);
                command.Parameters.AddWithValue("@ErrorMessage", errorMessage ?? (object)DBNull.Value);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        /// <summary>
        /// Get count of relationships by state
        /// </summary>
        public Dictionary<ETLRelationshipState, int> GetStateCounts()
        {
            var counts = new Dictionary<ETLRelationshipState, int>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT State, COUNT(*) as Count FROM ETLRelationship GROUP BY State", 
                (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var stateStr = GetSafeString(reader, "State");
                        var count = GetSafeInt(reader, "Count");
                        if (Enum.TryParse<ETLRelationshipState>(stateStr, out var state))
                        {
                            counts[state] = count;
                        }
                    }
                }
            }
            CloseConnection();
            return counts;
        }

        private ETLRelationship MapFromReader(System.Data.IDataReader reader)
        {
            return new ETLRelationship
            {
                Id = GetSafeLong(reader, "Id"),
                OldEntityId = GetSafeLong(reader, "OldEntityId"),
                NewEntityId = GetSafeLong(reader, "NewEntityId"),
                OldEntityType = GetSafeString(reader, "OldEntityType"),
                NewEntityType = GetSafeString(reader, "NewEntityType"),
                State = GetSafeEnum<ETLRelationshipState>(reader, "State", ETLRelationshipState.Pending),
                CreatedAt = GetSafeDateTime(reader, "CreatedAt"),
                UpdatedAt = GetSafeDateTime(reader, "UpdatedAt"),
                ErrorMessage = GetSafeString(reader, "ErrorMessage"),
                Metadata = GetSafeString(reader, "Metadata"),
                TransformationName = GetSafeString(reader, "TransformationName"),
                TransformationVersion = GetSafeString(reader, "TransformationVersion")
            };
        }

        private void AddParameters(SqliteCommand command, ETLRelationship entity)
        {
            command.Parameters.AddWithValue("@OldEntityId", entity.OldEntityId);
            command.Parameters.AddWithValue("@NewEntityId", entity.NewEntityId);
            command.Parameters.AddWithValue("@OldEntityType", entity.OldEntityType);
            command.Parameters.AddWithValue("@NewEntityType", entity.NewEntityType);
            command.Parameters.AddWithValue("@State", entity.State.ToString());
            command.Parameters.AddWithValue("@CreatedAt", entity.CreatedAt);
            command.Parameters.AddWithValue("@UpdatedAt", entity.UpdatedAt);
            command.Parameters.AddWithValue("@ErrorMessage", entity.ErrorMessage ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Metadata", entity.Metadata ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@TransformationName", entity.TransformationName);
            command.Parameters.AddWithValue("@TransformationVersion", entity.TransformationVersion);
        }
    }
}
