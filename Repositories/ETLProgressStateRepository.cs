using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models;
using Microsoft.Data.Sqlite;

namespace HeliosETL.Repositories
{
    /// <summary>
    /// Repository for managing ETL transformation progress state
    /// </summary>
    public class ETLProgressStateRepository : Repository<ETLProgressState>
    {
        public ETLProgressStateRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<ETLProgressState> GetAll()
        {
            var progressStates = new List<ETLProgressState>();
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLProgressState ORDER BY StartedAt DESC", (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        progressStates.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return progressStates;
        }

        public override ETLProgressState GetById(int id)
        {
            ETLProgressState progressState = null;
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLProgressState WHERE Id = @id", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        progressState = MapFromReader(reader);
                    }
                }
            }
            CloseConnection();
            return progressState;
        }

        /// <summary>
        /// Get progress state by run ID
        /// </summary>
        public ETLProgressState GetByRunId(string runId)
        {
            ETLProgressState progressState = null;
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLProgressState WHERE RunId = @runId", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@runId", runId);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        progressState = MapFromReader(reader);
                    }
                }
            }
            CloseConnection();
            return progressState;
        }

        /// <summary>
        /// Get all progress states for a specific transformation
        /// </summary>
        public IEnumerable<ETLProgressState> GetByTransformation(string transformationName)
        {
            var progressStates = new List<ETLProgressState>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLProgressState WHERE TransformationName = @transformationName ORDER BY StartedAt DESC", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@transformationName", transformationName);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        progressStates.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return progressStates;
        }

        /// <summary>
        /// Get the latest progress state for a transformation
        /// </summary>
        public ETLProgressState GetLatestByTransformation(string transformationName)
        {
            ETLProgressState progressState = null;
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLProgressState WHERE TransformationName = @transformationName ORDER BY StartedAt DESC LIMIT 1", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@transformationName", transformationName);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        progressState = MapFromReader(reader);
                    }
                }
            }
            CloseConnection();
            return progressState;
        }

        /// <summary>
        /// Get progress states by status
        /// </summary>
        public IEnumerable<ETLProgressState> GetByStatus(ETLProgressStatus status)
        {
            var progressStates = new List<ETLProgressState>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLProgressState WHERE Status = @status ORDER BY StartedAt DESC", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@status", status.ToString());
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        progressStates.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return progressStates;
        }

        /// <summary>
        /// Get resumable transformations (failed or paused with CanResume = true)
        /// </summary>
        public IEnumerable<ETLProgressState> GetResumableTransformations()
        {
            var progressStates = new List<ETLProgressState>();
            OpenConnection();
            using (var command = new SqliteCommand(
                "SELECT * FROM ETLProgressState WHERE CanResume = 1 AND (Status = 'Failed' OR Status = 'Paused') ORDER BY StartedAt DESC", 
                (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        progressStates.Add(MapFromReader(reader));
                    }
                }
            }
            CloseConnection();
            return progressStates;
        }

        public override void Add(ETLProgressState entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand(@"
                INSERT INTO ETLProgressState 
                (TransformationName, RunId, Status, StartedAt, CompletedAt, LastUpdatedAt, TotalItems, 
                 ProcessedItems, FailedItems, SkippedItems, CurrentPhase, ProgressMessage, ErrorMessage, 
                 ErrorStackTrace, Configuration, Metadata, TransformationVersion, LastProcessedItemId, 
                 CanResume, RetryCount, MaxRetries) 
                VALUES 
                (@TransformationName, @RunId, @Status, @StartedAt, @CompletedAt, @LastUpdatedAt, @TotalItems, 
                 @ProcessedItems, @FailedItems, @SkippedItems, @CurrentPhase, @ProgressMessage, @ErrorMessage, 
                 @ErrorStackTrace, @Configuration, @Metadata, @TransformationVersion, @LastProcessedItemId, 
                 @CanResume, @RetryCount, @MaxRetries)", 
                (SqliteConnection)_connection))
            {
                AddParameters(command, entity);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(ETLProgressState entity)
        {
            entity.LastUpdatedAt = DateTime.UtcNow;
            OpenConnection();
            using (var command = new SqliteCommand(@"
                UPDATE ETLProgressState SET 
                    Status = @Status, 
                    CompletedAt = @CompletedAt, 
                    LastUpdatedAt = @LastUpdatedAt, 
                    TotalItems = @TotalItems, 
                    ProcessedItems = @ProcessedItems, 
                    FailedItems = @FailedItems, 
                    SkippedItems = @SkippedItems, 
                    CurrentPhase = @CurrentPhase, 
                    ProgressMessage = @ProgressMessage, 
                    ErrorMessage = @ErrorMessage, 
                    ErrorStackTrace = @ErrorStackTrace, 
                    Configuration = @Configuration, 
                    Metadata = @Metadata, 
                    LastProcessedItemId = @LastProcessedItemId, 
                    CanResume = @CanResume, 
                    RetryCount = @RetryCount, 
                    MaxRetries = @MaxRetries 
                WHERE Id = @Id", 
                (SqliteConnection)_connection))
            {
                AddParameters(command, entity);
                command.Parameters.AddWithValue("@Id", entity.Id);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(ETLProgressState entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand("DELETE FROM ETLProgressState WHERE Id = @Id", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@Id", entity.Id);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        /// <summary>
        /// Update progress information
        /// </summary>
        public void UpdateProgress(string runId, long processedItems, long failedItems, long skippedItems, 
            string currentPhase = null, string progressMessage = null, long? lastProcessedItemId = null)
        {
            OpenConnection();
            using (var command = new SqliteCommand(@"
                UPDATE ETLProgressState SET 
                    ProcessedItems = @ProcessedItems, 
                    FailedItems = @FailedItems, 
                    SkippedItems = @SkippedItems, 
                    CurrentPhase = COALESCE(@CurrentPhase, CurrentPhase), 
                    ProgressMessage = COALESCE(@ProgressMessage, ProgressMessage), 
                    LastProcessedItemId = COALESCE(@LastProcessedItemId, LastProcessedItemId), 
                    LastUpdatedAt = @LastUpdatedAt 
                WHERE RunId = @RunId", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@RunId", runId);
                command.Parameters.AddWithValue("@ProcessedItems", processedItems);
                command.Parameters.AddWithValue("@FailedItems", failedItems);
                command.Parameters.AddWithValue("@SkippedItems", skippedItems);
                command.Parameters.AddWithValue("@CurrentPhase", currentPhase ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ProgressMessage", progressMessage ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LastProcessedItemId", lastProcessedItemId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LastUpdatedAt", DateTime.UtcNow);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        /// <summary>
        /// Update status and completion information
        /// </summary>
        public void UpdateStatus(string runId, ETLProgressStatus status, string errorMessage = null, string errorStackTrace = null)
        {
            OpenConnection();
            using (var command = new SqliteCommand(@"
                UPDATE ETLProgressState SET 
                    Status = @Status, 
                    CompletedAt = @CompletedAt, 
                    ErrorMessage = @ErrorMessage, 
                    ErrorStackTrace = @ErrorStackTrace, 
                    LastUpdatedAt = @LastUpdatedAt 
                WHERE RunId = @RunId", 
                (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@RunId", runId);
                command.Parameters.AddWithValue("@Status", status.ToString());
                command.Parameters.AddWithValue("@CompletedAt", 
                    (status == ETLProgressStatus.Completed || status == ETLProgressStatus.Failed || status == ETLProgressStatus.Cancelled) 
                        ? DateTime.UtcNow : (object)DBNull.Value);
                command.Parameters.AddWithValue("@ErrorMessage", errorMessage ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ErrorStackTrace", errorStackTrace ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LastUpdatedAt", DateTime.UtcNow);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        /// <summary>
        /// Get summary statistics for all transformations
        /// </summary>
        public Dictionary<string, object> GetSummaryStatistics()
        {
            var stats = new Dictionary<string, object>();
            OpenConnection();
            
            // Get status counts
            using (var command = new SqliteCommand(
                "SELECT Status, COUNT(*) as Count FROM ETLProgressState GROUP BY Status", 
                (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var status = GetSafeString(reader, "Status");
                        var count = GetSafeInt(reader, "Count");
                        stats[$"Status_{status}"] = count;
                    }
                }
            }
            
            // Get transformation counts
            using (var command = new SqliteCommand(
                "SELECT TransformationName, COUNT(*) as Count FROM ETLProgressState GROUP BY TransformationName", 
                (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var transformation = GetSafeString(reader, "TransformationName");
                        var count = GetSafeInt(reader, "Count");
                        stats[$"Transformation_{transformation}"] = count;
                    }
                }
            }
            
            CloseConnection();
            return stats;
        }

        private ETLProgressState MapFromReader(System.Data.IDataReader reader)
        {
            return new ETLProgressState
            {
                Id = GetSafeLong(reader, "Id"),
                TransformationName = GetSafeString(reader, "TransformationName"),
                RunId = GetSafeString(reader, "RunId"),
                Status = GetSafeEnum<ETLProgressStatus>(reader, "Status", ETLProgressStatus.NotStarted),
                StartedAt = GetSafeDateTime(reader, "StartedAt"),
                CompletedAt = GetSafeNullable<DateTime>(reader, "CompletedAt"),
                LastUpdatedAt = GetSafeDateTime(reader, "LastUpdatedAt"),
                TotalItems = GetSafeLong(reader, "TotalItems"),
                ProcessedItems = GetSafeLong(reader, "ProcessedItems"),
                FailedItems = GetSafeLong(reader, "FailedItems"),
                SkippedItems = GetSafeLong(reader, "SkippedItems"),
                CurrentPhase = GetSafeString(reader, "CurrentPhase"),
                ProgressMessage = GetSafeString(reader, "ProgressMessage"),
                ErrorMessage = GetSafeString(reader, "ErrorMessage"),
                ErrorStackTrace = GetSafeString(reader, "ErrorStackTrace"),
                Configuration = GetSafeString(reader, "Configuration"),
                Metadata = GetSafeString(reader, "Metadata"),
                TransformationVersion = GetSafeString(reader, "TransformationVersion"),
                LastProcessedItemId = GetSafeNullable<long>(reader, "LastProcessedItemId"),
                CanResume = GetSafeBool(reader, "CanResume"),
                RetryCount = GetSafeInt(reader, "RetryCount"),
                MaxRetries = GetSafeInt(reader, "MaxRetries")
            };
        }

        private void AddParameters(SqliteCommand command, ETLProgressState entity)
        {
            command.Parameters.AddWithValue("@TransformationName", entity.TransformationName);
            command.Parameters.AddWithValue("@RunId", entity.RunId);
            command.Parameters.AddWithValue("@Status", entity.Status.ToString());
            command.Parameters.AddWithValue("@StartedAt", entity.StartedAt);
            command.Parameters.AddWithValue("@CompletedAt", entity.CompletedAt ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@LastUpdatedAt", entity.LastUpdatedAt);
            command.Parameters.AddWithValue("@TotalItems", entity.TotalItems);
            command.Parameters.AddWithValue("@ProcessedItems", entity.ProcessedItems);
            command.Parameters.AddWithValue("@FailedItems", entity.FailedItems);
            command.Parameters.AddWithValue("@SkippedItems", entity.SkippedItems);
            command.Parameters.AddWithValue("@CurrentPhase", entity.CurrentPhase ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProgressMessage", entity.ProgressMessage ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ErrorMessage", entity.ErrorMessage ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ErrorStackTrace", entity.ErrorStackTrace ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Configuration", entity.Configuration ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Metadata", entity.Metadata ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@TransformationVersion", entity.TransformationVersion);
            command.Parameters.AddWithValue("@LastProcessedItemId", entity.LastProcessedItemId ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CanResume", entity.CanResume);
            command.Parameters.AddWithValue("@RetryCount", entity.RetryCount);
            command.Parameters.AddWithValue("@MaxRetries", entity.MaxRetries);
        }
    }
}
