using System.Data.Common;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1;

public class CommandesRepository : Repository<Commandes>
{
    public CommandesRepository(DbConnection connection) : base(connection)
    {
    }

    public override IEnumerable<Commandes> GetAll()
    {
        HashSet<Commandes> commandes = new HashSet<Commandes>();

        OpenConnection();
        using (var command = new SqlCommand("SELECT * FROM Commandes", (SqlConnection)_connection))
        {
            using (var reader = command.ExecuteReader())
            {
                while (reader.Read())
                {
                    commandes.Add(new Commandes
                    {
                        IdCommandes = GetSafeInt(reader, "IdCommandes"),
                        CtNum = GetSafeString(reader, "ct_num"),
                        Client = GetSafeString(reader, "client"),
                        Commande = GetSafeString(reader, "commande"),
                        IdTickets = GetSafeInt(reader, "idTickets"),
                        Pole = GetSafeString(reader, "pole"),
                        Commercial = GetSafeString(reader, "commercial"),
                        Titre = GetSafeString(reader, "titre"),
                        ContactNom = GetSafeString(reader, "contactNom"),
                        ContactTel = GetSafeString(reader, "contactTel"),
                        ContactEmail = GetSafeString(reader, "contactEmail"),
                        Adresse = GetSafeString(reader, "adresse"),
                        DateLivraison = GetSafeDateTime(reader, "dateLivraison"),
                        RespectDateLivraison = GetSafeBool(reader, "respectLivraison"),
                        Type = GetSafeString(reader, "type"),
                        Planification = GetSafeBool(reader, "planification"),
                        Note = GetSafeString(reader, "note"),
                        Bailleur = GetSafeInt(reader, "bailleur"),
                        Facture = GetSafeString(reader, "facture"),
                        Tech1 = GetSafeString(reader, "tech1"),
                        Tech2 = GetSafeString(reader, "tech2"),
                        DateFin = GetSafeDateTime(reader, "dateFin"),
                        MajAbonne = GetSafeBool(reader, "majAbo"),
                        SuiviMail = GetSafeBool(reader, "suiviMail"),
                        DateDemandePlanif = GetSafeDateTime(reader, "dateDemandePlanif"),
                        AdvCheck = GetSafeBool(reader, "adv_check"),
                        AdvUpdate = GetSafeBool(reader, "adv_update"),
                        AdvNote = GetSafeString(reader, "adv_note")
                    });
                }
            }
        }
        CloseConnection();
        
        return commandes;
    }

    public override Commandes GetById(int id)
    {
        Commandes commande = null;

        OpenConnection();
        using (var command = new SqlCommand("SELECT * FROM Commandes WHERE IdCommandes = @id", (SqlConnection)_connection))
        {
            command.Parameters.AddWithValue("@id", id);
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    commande = new Commandes
                    {
                        IdCommandes = GetSafeInt(reader, "IdCommandes"),
                        CtNum = GetSafeString(reader, "ct_num"),
                        Client = GetSafeString(reader, "client"),
                        Commande = GetSafeString(reader, "commande"),
                        IdTickets = GetSafeInt(reader, "idTickets"),
                        Pole = GetSafeString(reader, "pole"),
                        Commercial = GetSafeString(reader, "commercial"),
                        Titre = GetSafeString(reader, "titre"),
                        ContactNom = GetSafeString(reader, "contactNom"),
                        ContactTel = GetSafeString(reader, "contactTel"),
                        ContactEmail = GetSafeString(reader, "contactEmail"),
                        Adresse = GetSafeString(reader, "adresse"),
                        DateLivraison = GetSafeDateTime(reader, "dateLivraison"),
                        RespectDateLivraison = GetSafeBool(reader, "respectLivraison"),
                        Type = GetSafeString(reader, "type"),
                        Planification = GetSafeBool(reader, "planification"),
                        Note = GetSafeString(reader, "note"),
                        Bailleur = GetSafeInt(reader, "bailleur"),
                        Facture = GetSafeString(reader, "facture"),
                        Tech1 = GetSafeString(reader, "tech1"),
                        Tech2 = GetSafeString(reader, "tech2"),
                        DateFin = GetSafeDateTime(reader, "dateFin"),
                        MajAbonne = GetSafeBool(reader, "majAbo"),
                        SuiviMail = GetSafeBool(reader, "suiviMail"),
                        DateDemandePlanif = GetSafeDateTime(reader, "dateDemandePlanif"),
                        AdvCheck = GetSafeBool(reader, "adv_check"),
                        AdvUpdate = GetSafeBool(reader, "adv_update"),
                        AdvNote = GetSafeString(reader, "adv_note")
                    };
                }
            }
        }
        CloseConnection();
        
        return commande;
    }

    public override void Add(Commandes entity)
    {
        throw new NotImplementedException();
    }

    public override void Update(Commandes entity)
    {
        throw new NotImplementedException();
    }

    public override void Delete(Commandes entity)
    {
        throw new NotImplementedException();
    }
}