using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypeProjetRepository : Repository<TypeProjet>
    {
        public TypeProjetRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<TypeProjet> GetAll()
        {
            var typeProjets = new List<TypeProjet>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_projet_type", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        typeProjets.Add(new TypeProjet
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        });
                    }
                }
            }
            CloseConnection();
            
            foreach (var typeProjet in typeProjets)
            {
                typeProjet.DomainesMetier = GetDomainesMetierByTypeProjet(typeProjet.Oid);
            }
            
            return typeProjets;
        }

        public override TypeProjet GetById(int id)
        {
            TypeProjet typeProjet = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_projet_type WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        typeProjet = new TypeProjet
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        };
                    }
                }
            }
            CloseConnection();
            
            if (typeProjet != null)
            {
                typeProjet.DomainesMetier = GetDomainesMetierByTypeProjet(typeProjet.Oid);
            }
            
            return typeProjet;
        }
        
        public HashSet<DomaineMetier> GetDomainesMetierByTypeProjet(long oidTypeProjet)
        {
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            OpenConnection();
            using (var command = new MySqlCommand(@"
                    SELECT * FROM hls_domaine_metier 
                             WHERE Oid IN (
                             SELECT domaine_metier_oid FROM hls_projet_type_has_domaine_metier WHERE projet_type_oid = @oidTypeProjet
                   )", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidTypeProjet", oidTypeProjet);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        domainesMetier.Add(new DomaineMetier
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }
            CloseConnection();
            return domainesMetier;
        }

        public override void Add(TypeProjet entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // 1. Insert main entity
                    using (var command = new MySqlCommand("INSERT INTO hls_projet_type (Oid, __version, Code, Libelle, Description, Obsolete) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete)", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.Parameters.AddWithValue("@__version", entity.__version);
                        command.Parameters.AddWithValue("@Code", entity.Code);
                        command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                        command.Parameters.AddWithValue("@Description", entity.Description);
                        command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                        command.ExecuteNonQuery();
                    }

                    // 2. Handle DomaineMetier relationships
                    if (entity.DomainesMetier != null && entity.DomainesMetier.Any())
                    {
                        foreach (var domaineMetier in entity.DomainesMetier)
                        {
                            using (var relationCommand = new MySqlCommand(
                                "INSERT INTO hls_projet_type_has_domaine_metier (projet_type_oid, domaine_metier_oid) VALUES (@projetTypeOid, @domaineMetierOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                relationCommand.Parameters.AddWithValue("@projetTypeOid", entity.Oid);
                                relationCommand.Parameters.AddWithValue("@domaineMetierOid", domaineMetier.Oid);
                                relationCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        public override void Update(TypeProjet entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // 1. Update main entity
                    using (var command = new MySqlCommand("UPDATE hls_projet_type SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@__version", entity.__version);
                        command.Parameters.AddWithValue("@Code", entity.Code);
                        command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                        command.Parameters.AddWithValue("@Description", entity.Description);
                        command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.ExecuteNonQuery();
                    }

                    // 2. Sync DomaineMetier relationships (remove old, add new)
                    // First, remove existing relationships
                    using (var deleteCommand = new MySqlCommand(
                        "DELETE FROM hls_projet_type_has_domaine_metier WHERE projet_type_oid = @projetTypeOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deleteCommand.Parameters.AddWithValue("@projetTypeOid", entity.Oid);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // Then, add current relationships
                    if (entity.DomainesMetier != null && entity.DomainesMetier.Any())
                    {
                        foreach (var domaineMetier in entity.DomainesMetier)
                        {
                            using (var insertCommand = new MySqlCommand(
                                "INSERT INTO hls_projet_type_has_domaine_metier (projet_type_oid, domaine_metier_oid) VALUES (@projetTypeOid, @domaineMetierOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@projetTypeOid", entity.Oid);
                                insertCommand.Parameters.AddWithValue("@domaineMetierOid", domaineMetier.Oid);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        public override void Delete(TypeProjet entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // First, delete relationships
                    using (var deleteRelationsCommand = new MySqlCommand("DELETE FROM hls_projet_type_has_domaine_metier WHERE projet_type_oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        deleteRelationsCommand.Parameters.AddWithValue("@Oid", entity.Oid);
                        deleteRelationsCommand.ExecuteNonQuery();
                    }

                    // Then, delete main entity
                    using (var command = new MySqlCommand("DELETE FROM hls_projet_type WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.ExecuteNonQuery();
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        // TODO: ✅ COMPLETED - Relationship management methods for DomaineMetier relationships
        // - Add/Update methods now handle DomaineMetier relationships with transactions
        // - Added relationship management methods: Add, Remove, Sync
        public void AddDomaineMetierToTypeProjet(long oidTypeProjet, long oidDomaineMetier)
        {
            OpenConnection();
            using (var command = new MySqlCommand(
                "INSERT IGNORE INTO hls_projet_type_has_domaine_metier (projet_type_oid, domaine_metier_oid) VALUES (@projetTypeOid, @domaineMetierOid)",
                (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@projetTypeOid", oidTypeProjet);
                command.Parameters.AddWithValue("@domaineMetierOid", oidDomaineMetier);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public void RemoveDomaineMetierFromTypeProjet(long oidTypeProjet, long oidDomaineMetier)
        {
            OpenConnection();
            using (var command = new MySqlCommand(
                "DELETE FROM hls_projet_type_has_domaine_metier WHERE projet_type_oid = @projetTypeOid AND domaine_metier_oid = @domaineMetierOid",
                (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@projetTypeOid", oidTypeProjet);
                command.Parameters.AddWithValue("@domaineMetierOid", oidDomaineMetier);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public void SyncDomainesMetierForTypeProjet(long oidTypeProjet, HashSet<long> domaineMetierOids)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // Remove all existing relationships
                    using (var deleteCommand = new MySqlCommand(
                        "DELETE FROM hls_projet_type_has_domaine_metier WHERE projet_type_oid = @projetTypeOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deleteCommand.Parameters.AddWithValue("@projetTypeOid", oidTypeProjet);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // Add new relationships
                    if (domaineMetierOids != null && domaineMetierOids.Any())
                    {
                        foreach (var oidDomaineMetier in domaineMetierOids)
                        {
                            using (var insertCommand = new MySqlCommand(
                                "INSERT INTO hls_projet_type_has_domaine_metier (projet_type_oid, domaine_metier_oid) VALUES (@projetTypeOid, @domaineMetierOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@projetTypeOid", oidTypeProjet);
                                insertCommand.Parameters.AddWithValue("@domaineMetierOid", oidDomaineMetier);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }
    }
}