using System.Data.Common;
using HeliosETL.Models.v2;
using HeliosETL.Utils;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2;

public class ActiviteRepository : Repository<AbstractActivite>
{
    public ActiviteRepository(DbConnection connection) : base(connection)
    {
    }

    public override IEnumerable<AbstractActivite> GetAll()
    {
        HashSet<AbstractActivite> activites = new HashSet<AbstractActivite>();
        
        OpenConnection();
        using (var command = new MySqlCommand("SELECT * FROM hls_activite", (MySqlConnection)_connection))
        {
            using (var reader = command.ExecuteReader())
            {
                while (reader.Read())
                {
                    Statut status = Statut.BROUILLON;
                    EnumUtils.TryGetEnumFromDescription<Statut>(GetSafeString(reader, "statut"), out status);
                    
                    if(status.Equals(default(Statut)))
                    {
                        status = Statut.BROUILLON;
                    }
                    
                    activites.Add(new AbstractActivite
                    {
                        Oid = GetSafeLong(reader, "Oid"),
                        __version = GetSafeInt(reader, "__version"),
                        Code = GetSafeString(reader, "Code"),
                        Libelle = GetSafeString(reader, "Libelle"),
                        Description = GetSafeString(reader, "Description"),
                        DateCreation = GetSafeDateTime(reader, "DateCreation"),
                        DateModification = GetSafeDateTime(reader, "DateModification"),
                        DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                        DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                        DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                        DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                        Statut = status,
                        RelationsData = new ActiviteRelations
                        {
                            domaine_principal_oid = GetSafeLong(reader, "domaine_principal_oid"),
                            mission_type_oid = GetSafeLong(reader, "mission_type_oid"),
                            projet_type_oid = GetSafeLong(reader, "projet_type_oid")
                        }
                    });
                }
            }
        }
        CloseConnection();
        
        return activites;
    }

    public override AbstractActivite GetById(int id)
    {
        AbstractActivite activite = null;
        
        OpenConnection();
        using (var command = new MySqlCommand("SELECT * FROM hls_activite WHERE Oid = @id", (MySqlConnection)_connection))
        {
            command.Parameters.AddWithValue("@id", id);
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    Statut status = Statut.BROUILLON;
                    EnumUtils.TryGetEnumFromDescription<Statut>(GetSafeString(reader, "statut"), out status);
                    
                    if(status.Equals(default(Statut)))
                    {
                        status = Statut.BROUILLON;
                    }
                    
                    activite = new AbstractActivite
                    {
                        Oid = GetSafeLong(reader, "Oid"),
                        __version = GetSafeInt(reader, "__version"),
                        Code = GetSafeString(reader, "Code"),
                        Libelle = GetSafeString(reader, "Libelle"),
                        Description = GetSafeString(reader, "Description"),
                        DateCreation = GetSafeDateTime(reader, "DateCreation"),
                        DateModification = GetSafeDateTime(reader, "DateModification"),
                        DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                        DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                        DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                        DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                        Statut = status,
                        RelationsData = new ActiviteRelations
                        {
                            domaine_principal_oid = GetSafeLong(reader, "domaine_principal_oid"),
                            mission_type_oid = GetSafeLong(reader, "mission_type_oid"),
                            projet_type_oid = GetSafeLong(reader, "projet_type_oid")
                        }
                    };
                }
            }
        }
        CloseConnection();
        
        return activite;
    }

    public override void Add(AbstractActivite entity)
    {
        OpenConnection();
        using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
        {
            try
            {
                string statusDescription = entity.Statut.GetDescription();
                
                using (var command = new MySqlCommand(@"
                    INSERT INTO hls_activite (
                        Oid, __version, Code, Libelle, Description, 
                        DateCreation, DateModification, DatePrevisionnelleDebut, 
                        DatePrevisionnelleFin, DateEffectiveDebut, DateEffectiveFin, 
                        status, domaine_principal_oid, mission_type_oid, projet_type_oid
                    ) VALUES (
                        @Oid, @__version, @Code, @Libelle, @Description, 
                        @DateCreation, @DateModification, @DatePrevisionnelleDebut, 
                        @DatePrevisionnelleFin, @DateEffectiveDebut, @DateEffectiveFin, 
                        @status, @domaine_principal_oid, @mission_type_oid, @projet_type_oid
                    )", (MySqlConnection)_connection, transaction))
                {
                    command.Parameters.AddWithValue("@Oid", entity.Oid);
                    command.Parameters.AddWithValue("@__version", entity.__version);
                    command.Parameters.AddWithValue("@Code", entity.Code);
                    command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                    command.Parameters.AddWithValue("@Description", entity.Description);
                    command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                    command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                    command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                    command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                    command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                    command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                    command.Parameters.AddWithValue("@status", statusDescription);
                    command.Parameters.AddWithValue("@domaine_principal_oid", entity.RelationsData.domaine_principal_oid);
                    command.Parameters.AddWithValue("@mission_type_oid", entity.RelationsData.mission_type_oid);
                    command.Parameters.AddWithValue("@projet_type_oid", entity.RelationsData.projet_type_oid);
                    
                    command.ExecuteNonQuery();
                }
                
                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }
        CloseConnection();
    }

    public override void Update(AbstractActivite entity)
    {
        OpenConnection();
        using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
        {
            try
            {
                string statusDescription = entity.Statut.GetDescription();
                
                using (var command = new MySqlCommand(@"
                    UPDATE hls_activite SET 
                        __version = @__version,
                        Code = @Code,
                        Libelle = @Libelle,
                        Description = @Description,
                        DateCreation = @DateCreation,
                        DateModification = @DateModification,
                        DatePrevisionnelleDebut = @DatePrevisionnelleDebut,
                        DatePrevisionnelleFin = @DatePrevisionnelleFin,
                        DateEffectiveDebut = @DateEffectiveDebut,
                        DateEffectiveFin = @DateEffectiveFin,
                        status = @status,
                        domaine_principal_oid = @domaine_principal_oid,
                        mission_type_oid = @mission_type_oid,
                        projet_type_oid = @projet_type_oid
                    WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                {
                    command.Parameters.AddWithValue("@__version", entity.__version);
                    command.Parameters.AddWithValue("@Code", entity.Code);
                    command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                    command.Parameters.AddWithValue("@Description", entity.Description);
                    command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                    command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                    command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                    command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                    command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                    command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                    command.Parameters.AddWithValue("@status", statusDescription);
                    command.Parameters.AddWithValue("@domaine_principal_oid", entity.RelationsData.domaine_principal_oid);
                    command.Parameters.AddWithValue("@mission_type_oid", entity.RelationsData.mission_type_oid);
                    command.Parameters.AddWithValue("@projet_type_oid", entity.RelationsData.projet_type_oid);
                    command.Parameters.AddWithValue("@Oid", entity.Oid);
                    
                    command.ExecuteNonQuery();
                }
                
                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }
        CloseConnection();
    }

    public override void Delete(AbstractActivite entity)
    {
        OpenConnection();
        using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
        {
            try
            {
                using (var command = new MySqlCommand("DELETE FROM hls_activite WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                {
                    command.Parameters.AddWithValue("@Oid", entity.Oid);
                    command.ExecuteNonQuery();
                }
                
                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }
        CloseConnection();
    }
}