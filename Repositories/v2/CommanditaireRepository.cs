using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class CommanditaireRepository : Repository<Commanditaire>
    {
        public CommanditaireRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Commanditaire> GetAll()
        {
            var commanditaires = new List<Commanditaire>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_commanditaire", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        commanditaires.Add(new Commanditaire
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                            Data = GetSafeJSONAsDictionary(reader, "Data")
                            
                        });
                    }
                }
            }
            CloseConnection();
            
            foreach (var commanditaire in commanditaires)
            {
                commanditaire.Personnes = GetPersonnesByCommanditaire(commanditaire.Oid);
            }
            
            return commanditaires;
        }

        public HashSet<Personne> GetPersonnesByCommanditaire(long oidCommanditaire)
        {
            HashSet<Personne> personnes = new HashSet<Personne>();
            
            OpenConnection();
            using (var command = new MySqlCommand(@"
                    SELECT * FROM hls_personne 
                             WHERE Oid IN (
                             SELECT personne_oid FROM hls_commanditaire_has_personne WHERE commanditaire_oid = @oidCommanditaire
                   )", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidCommanditaire", oidCommanditaire);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        personnes.Add(new Personne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Prenom = GetSafeString(reader, "Prenom"),
                            Fonction = GetSafeString(reader, "Fonction"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                            ExternalSource = new SourceOfData()
                            {
                                Oid = GetSafeLong(reader, "src_oid"),
                                Kind = GetSafeString(reader, "src_kind"),
                                Name = GetSafeString(reader, "src_name"),
                                DateUpdated = GetSafeDateTime(reader, "src_date")
                            }
                        });
                    }
                }
            }

            return personnes;
        }

        public override Commanditaire GetById(int id)
        {
            Commanditaire commanditaire = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_commanditaire WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        commanditaire = new Commanditaire
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                            Data = GetSafeJSONAsDictionary(reader, "Data"),
                            ExternalSource = new SourceOfData()
                            {
                                Oid = GetSafeLong(reader, "src_oid"),
                                Kind = GetSafeString(reader, "src_kind"),
                                Name = GetSafeString(reader, "src_name"),
                                DateUpdated = GetSafeDateTime(reader, "src_date")
                            }
                        };
                    }
                }
            }
            CloseConnection();
            
            if (commanditaire != null)
            {
                commanditaire.Personnes = GetPersonnesByCommanditaire(commanditaire.Oid);
            }
            
            return commanditaire;
        }

        public override void Add(Commanditaire entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // 1. Insert main entity
                    using (var command = new MySqlCommand("INSERT INTO hls_commanditaire (Oid, __version, Externe, Nom, Email, Telephone) VALUES (@Oid, @__version, @Externe, @Nom, @Email, @Telephone)", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.Parameters.AddWithValue("@__version", entity.__version);
                        command.Parameters.AddWithValue("@Externe", entity.Externe);
                        command.Parameters.AddWithValue("@Nom", entity.Nom);
                        command.Parameters.AddWithValue("@Email", entity.Email);
                        command.Parameters.AddWithValue("@Telephone", entity.Telephone);
                        command.ExecuteNonQuery();
                    }

                    // 2. Handle Personne relationships
                    if (entity.Personnes != null && entity.Personnes.Any())
                    {
                        foreach (var personne in entity.Personnes)
                        {
                            using (var relationCommand = new MySqlCommand(
                                "INSERT INTO hls_commanditaire_has_personne (commanditaire_oid, personne_oid) VALUES (@commanditaireOid, @personneOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                relationCommand.Parameters.AddWithValue("@commanditaireOid", entity.Oid);
                                relationCommand.Parameters.AddWithValue("@personneOid", personne.Oid);
                                relationCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        public override void Update(Commanditaire entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // 1. Update main entity
                    using (var command = new MySqlCommand("UPDATE hls_commanditaire SET __version = @__version, Externe = @Externe, Nom = @Nom, Email = @Email, Telephone = @Telephone WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@__version", entity.__version);
                        command.Parameters.AddWithValue("@Externe", entity.Externe);
                        command.Parameters.AddWithValue("@Nom", entity.Nom);
                        command.Parameters.AddWithValue("@Email", entity.Email);
                        command.Parameters.AddWithValue("@Telephone", entity.Telephone);
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.ExecuteNonQuery();
                    }

                    // 2. Sync Personne relationships (remove old, add new)
                    // First, remove existing relationships
                    using (var deleteCommand = new MySqlCommand(
                        "DELETE FROM hls_commanditaire_has_personne WHERE commanditaire_oid = @commanditaireOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deleteCommand.Parameters.AddWithValue("@commanditaireOid", entity.Oid);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // Then, add current relationships
                    if (entity.Personnes != null && entity.Personnes.Any())
                    {
                        foreach (var personne in entity.Personnes)
                        {
                            using (var insertCommand = new MySqlCommand(
                                "INSERT INTO hls_commanditaire_has_personne (commanditaire_oid, personne_oid) VALUES (@commanditaireOid, @personneOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@commanditaireOid", entity.Oid);
                                insertCommand.Parameters.AddWithValue("@personneOid", personne.Oid);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        public override void Delete(Commanditaire entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // First, delete relationships
                    using (var deleteRelationsCommand = new MySqlCommand("DELETE FROM hls_commanditaire_has_personne WHERE commanditaire_oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        deleteRelationsCommand.Parameters.AddWithValue("@Oid", entity.Oid);
                        deleteRelationsCommand.ExecuteNonQuery();
                    }

                    // Then, delete main entity
                    using (var command = new MySqlCommand("DELETE FROM hls_commanditaire WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.ExecuteNonQuery();
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        // Relationship management methods
        public void AddPersonneToCommanditaire(long oidCommanditaire, long oidPersonne)
        {
            OpenConnection();
            using (var command = new MySqlCommand(
                "INSERT IGNORE INTO hls_commanditaire_has_personne (commanditaire_oid, personne_oid) VALUES (@commanditaireOid, @personneOid)",
                (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@commanditaireOid", oidCommanditaire);
                command.Parameters.AddWithValue("@personneOid", oidPersonne);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public void RemovePersonneFromCommanditaire(long oidCommanditaire, long oidPersonne)
        {
            OpenConnection();
            using (var command = new MySqlCommand(
                "DELETE FROM hls_commanditaire_has_personne WHERE commanditaire_oid = @commanditaireOid AND personne_oid = @personneOid",
                (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@commanditaireOid", oidCommanditaire);
                command.Parameters.AddWithValue("@personneOid", oidPersonne);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public void SyncPersonnesForCommanditaire(long oidCommanditaire, HashSet<long> personneOids)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // Remove all existing relationships
                    using (var deleteCommand = new MySqlCommand(
                        "DELETE FROM hls_commanditaire_has_personne WHERE commanditaire_oid = @commanditaireOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deleteCommand.Parameters.AddWithValue("@commanditaireOid", oidCommanditaire);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // Add new relationships
                    if (personneOids != null && personneOids.Any())
                    {
                        foreach (var oidPersonne in personneOids)
                        {
                            using (var insertCommand = new MySqlCommand(
                                "INSERT INTO hls_commanditaire_has_personne (commanditaire_oid, personne_oid) VALUES (@commanditaireOid, @personneOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@commanditaireOid", oidCommanditaire);
                                insertCommand.Parameters.AddWithValue("@personneOid", oidPersonne);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }
    }
}