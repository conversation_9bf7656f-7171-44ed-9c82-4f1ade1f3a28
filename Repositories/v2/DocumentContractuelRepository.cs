using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2;

public class DocumentContractuelRepository : Repository<DocumentContractuel>
{
    public DocumentContractuelRepository(DbConnection connection) : base(connection)
    {
    }

    public override IEnumerable<DocumentContractuel> GetAll()
    {
        HashSet<DocumentContractuel> documentContractuels = new HashSet<DocumentContractuel>();

        OpenConnection();
        using (var command = new MySqlCommand("SELECT * FROM hls_document_contractuel", (MySqlConnection)_connection))
        {
            using (var reader = command.ExecuteReader())
            {
                while (reader.Read())
                {
                    documentContractuels.Add(new DocumentContractuel
                    {
                        Oid = GetSafeLong(reader, "Oid"),
                        __version = GetSafeInt(reader, "__version"),
                        Externe = GetSafeBool(reader, "Externe"),
                        Libelle = GetSafeString(reader, "Libelle"),
                        Description = GetSafeString(reader, "Description"),
                        DateCreation = GetSafeDateTime(reader, "DateCreation"),
                        DateModification = GetSafeDateTime(reader, "DateModification"),
                        DateDebutValidite = GetSafeDateTime(reader, "DateDebutValidite"),
                        DateFinValidite = GetSafeDateTime(reader, "DateFinValidite"),
                        Data = GetSafeJSONAsDictionary(reader, "Data"),
                        IssueOid = GetSafeLong(reader, "issue_oid"),
                        DocumentOid = GetSafeLong(reader, "document_oid"),
                        ExternalSource = new SourceOfData()
                        {
                            Oid = GetSafeLong(reader, "src_oid"),
                            Kind = GetSafeString(reader, "src_kind"),
                            Name = GetSafeString(reader, "src_name"),
                            DateUpdated = GetSafeDateTime(reader, "src_date")
                        }
                    });
                }
            }
        }
        
        return documentContractuels;
    }

    public override DocumentContractuel GetById(int id)
    {
        DocumentContractuel documentContractuel = null;
        
        OpenConnection();
        using (var command = new MySqlCommand("SELECT * FROM hls_document_contractuel WHERE Oid = @id", (MySqlConnection)_connection))
        {
            command.Parameters.AddWithValue("@id", id);
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    documentContractuel = new DocumentContractuel
                    {
                        Oid = GetSafeLong(reader, "Oid"),
                        __version = GetSafeInt(reader, "__version"),
                        Externe = GetSafeBool(reader, "Externe"),
                        Libelle = GetSafeString(reader, "Libelle"),
                        Description = GetSafeString(reader, "Description"),
                        DateCreation = GetSafeDateTime(reader, "DateCreation"),
                        DateModification = GetSafeDateTime(reader, "DateModification"),
                        DateDebutValidite = GetSafeDateTime(reader, "DateDebutValidite"),
                        DateFinValidite = GetSafeDateTime(reader, "DateFinValidite"),
                        Data = GetSafeJSONAsDictionary(reader, "Data"),
                        IssueOid = GetSafeLong(reader, "issue_oid"),
                        DocumentOid = GetSafeLong(reader, "document_oid"),
                        ExternalSource = new SourceOfData()
                        {
                            Oid = GetSafeLong(reader, "src_oid"),
                            Kind = GetSafeString(reader, "src_kind"),
                            Name = GetSafeString(reader, "src_name"),
                            DateUpdated = GetSafeDateTime(reader, "src_date")
                        }
                    };
                }
            }
        }
        
        return documentContractuel;
    }

    public override void Add(DocumentContractuel entity)
    {
        throw new NotImplementedException();
    }

    public override void Update(DocumentContractuel entity)
    {
        throw new NotImplementedException();
    }

    public override void Delete(DocumentContractuel entity)
    {
        throw new NotImplementedException();
    }
}