using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueRepository : Repository<AbstractIssue>
    {
        public IssueRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<AbstractIssue> GetAll()
        {
            return GetAll(maxDepth: 1);
        }

        public IEnumerable<AbstractIssue> GetAll(int maxDepth = 1)
        {
            var issues = new List<AbstractIssue>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        issues.Add(new AbstractIssue
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes"),
                            RelationsActivite = new RelationsActivite
                            {
                                activite_oid = GetSafeLong(reader, "activite_oid"),
                                issue_parente_oid = GetSafeLong(reader, "issue_parente_oid"),
                                commanditaire_oid = GetSafeLong(reader, "commanditaire_oid"),
                                demandeur_oid = GetSafeLong(reader, "demandeur_oid"),
                                intervenant_principal_oid = GetSafeLong(reader, "intervenant_principal_oid"),
                                origine_oid = GetSafeLong(reader, "origine_oid"),
                                priorite_oid = GetSafeLong(reader, "priorite_oid"),
                                statut_oid = GetSafeLong(reader, "statut_oid"),
                                redacteur_oid = GetSafeLong(reader, "redacteur_oid")
                            }
                        });
                    }
                }
            }
            CloseConnection();

            // For each issue, create a fresh visited IDs set to prevent cross-contamination
            foreach (var issue in issues)
            {
                FillRelationFields(issue, maxDepth, new HashSet<long>());
            }
            
            return issues;
        }

        public override AbstractIssue GetById(int id)
        {
            return GetById(id, includeRelations: true, maxDepth: 1);
        }

        public AbstractIssue GetById(int id, bool includeRelations = true, int maxDepth = 1)
        {
            // Use a HashSet to track already visited issue IDs to prevent circular references
            return GetByIdInternal(id, includeRelations, maxDepth, new HashSet<long>());
        }

        private AbstractIssue GetByIdInternal(int id, bool includeRelations, int maxDepth, HashSet<long> visitedIds)
        {
            // Return null if ID is 0 or we've already visited this ID
            if (id <= 0 || visitedIds.Contains(id))
                return null;

            // Add the current ID to visited set
            visitedIds.Add(id);

            AbstractIssue issue = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        issue = new AbstractIssue
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes"),
                            RelationsActivite = new RelationsActivite
                            {
                                activite_oid = GetSafeLong(reader, "activite_oid"),
                                issue_parente_oid = GetSafeLong(reader, "issue_parente_oid"),
                                commanditaire_oid = GetSafeLong(reader, "commanditaire_oid"),
                                demandeur_oid = GetSafeLong(reader, "demandeur_oid"),
                                intervenant_principal_oid = GetSafeLong(reader, "intervenant_principal_oid"),
                                origine_oid = GetSafeLong(reader, "origine_oid"),
                                priorite_oid = GetSafeLong(reader, "priorite_oid"),
                                statut_oid = GetSafeLong(reader, "statut_oid"),
                                redacteur_oid = GetSafeLong(reader, "redacteur_oid")
                            }
                        };
                    }
                }
            }
            CloseConnection();

            if (issue != null && includeRelations)
            {
                FillRelationFields(issue, maxDepth, visitedIds);
            }
            
            return issue;
        }

        private HashSet<AbstractIssue> GetIssueChildrens(long issueOid, int maxDepth = 0, HashSet<long> visitedIds = null)
        {
            // Initialize visitedIds if null
            visitedIds = visitedIds ?? new HashSet<long>();

            HashSet<AbstractIssue> issuesChildrens = new HashSet<AbstractIssue>();

            OpenConnection();
            using (var command = new MySqlCommand(@"
                SELECT i.* FROM hls_issue i
                INNER JOIN hls_issue_relation ihi ON i.Oid = ihi.cible_oid
                WHERE ihi.source_oid = @issueOid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@issueOid", issueOid);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var childOid = GetSafeLong(reader, "Oid");

                        // Skip if we've already processed this issue to avoid circular references
                        if (visitedIds.Contains(childOid))
                            continue;

                        var issueChild = new AbstractIssue
                        {
                            Oid = childOid,
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes"),
                            RelationsActivite = new RelationsActivite
                            {
                                activite_oid = GetSafeLong(reader, "activite_oid"),
                                issue_parente_oid = GetSafeLong(reader, "issue_parente_oid"),
                                commanditaire_oid = GetSafeLong(reader, "commanditaire_oid"),
                                demandeur_oid = GetSafeLong(reader, "demandeur_oid"),
                                intervenant_principal_oid = GetSafeLong(reader, "intervenant_principal_oid"),
                                origine_oid = GetSafeLong(reader, "origine_oid"),
                                priorite_oid = GetSafeLong(reader, "priorite_oid"),
                                statut_oid = GetSafeLong(reader, "statut_oid"),
                                redacteur_oid = GetSafeLong(reader, "redacteur_oid")
                            }
                        };

                        issuesChildrens.Add(issueChild);
                    }
                }
            }
            CloseConnection();

            // Fill relations for each child, but with reduced depth
            foreach (var issueChild in issuesChildrens)
            {
                // Copy the visited IDs set to avoid modifying the original
                var childVisitedIds = new HashSet<long>(visitedIds);
                FillRelationFields(issueChild, maxDepth, childVisitedIds);
            }
            
            return issuesChildrens;
        }

        private void FillRelationFields(AbstractIssue issue, int maxDepth = 1, HashSet<long> visitedIds = null)
        {
            // Initialize visitedIds if null
            visitedIds = visitedIds ?? new HashSet<long>();

            // Add current issue to visited set
            visitedIds.Add(issue.Oid);

            // Load simple references (no recursion risk)
            if (issue.RelationsActivite.activite_oid > 0)
                issue.Activite = new ActiviteRepository(_connection).GetById((int)issue.RelationsActivite.activite_oid);

            if (issue.RelationsActivite.commanditaire_oid > 0)
                issue.Commanditaire = new CommanditaireRepository(_connection).GetById((int)issue.RelationsActivite.commanditaire_oid);

            if (issue.RelationsActivite.demandeur_oid > 0)
                issue.Demandeur = new PersonneRepository(_connection).GetById((int)issue.RelationsActivite.demandeur_oid);

            if (issue.RelationsActivite.intervenant_principal_oid > 0)
                issue.IntervenantPrincipal = new PersonneRepository(_connection).GetById((int)issue.RelationsActivite.intervenant_principal_oid);

            if (issue.RelationsActivite.origine_oid > 0)
                issue.Origine = new IssueOrigineRepository(_connection).GetById((int)issue.RelationsActivite.origine_oid);

            if (issue.RelationsActivite.priorite_oid > 0)
                issue.Priorite = new IssuePrioriteRepository(_connection).GetById((int)issue.RelationsActivite.priorite_oid);

            if (issue.RelationsActivite.statut_oid > 0)
                issue.Statut = new IssueStatutRepository(_connection).GetById((int)issue.RelationsActivite.statut_oid);

            if (issue.RelationsActivite.redacteur_oid > 0)
                issue.Redacteur = new PersonneRepository(_connection).GetById((int)issue.RelationsActivite.redacteur_oid);

            // Load collections (no recursion)
            issue.Intervenants = GetIntervenantsByIssue(issue.Oid);
            issue.PieceJointes = GetPieceJointesByIssue(issue.Oid);
            issue.JournalDetails = GetJournalDetailsByIssue(issue.Oid);
            issue.DocumentsContractuels = GetDocumentsContractuelsByIssue(issue.Oid);

            // Load parent issue with direct access (skipping depth check since it's a single relation)
            // but still checking to avoid circular references
            if (issue.RelationsActivite.issue_parente_oid > 0 && !visitedIds.Contains(issue.RelationsActivite.issue_parente_oid))
            {
                // Load parent without its children to avoid extra nesting
                issue.IssueParente = GetByIdInternal((int)issue.RelationsActivite.issue_parente_oid, true, 0, visitedIds);
            }

            // Only load recursive relationships if we haven't reached max depth
            if (maxDepth > 0)
            {
                // Load children with one level less depth
                issue.Issues = GetIssueChildrens(issue.Oid, maxDepth - 1, visitedIds);
                issue.RelationsEntrantes = GetIssueEntrante(issue.Oid, maxDepth - 1, visitedIds);
                issue.RelationsSortantes = GetIssueSortante(issue.Oid, maxDepth - 1, visitedIds);
            }
            else
            {
                // Initialize empty collections to avoid null references
                issue.Issues = new HashSet<AbstractIssue>();
                issue.RelationsEntrantes = new HashSet<AbstractIssue>();
                issue.RelationsSortantes = new HashSet<AbstractIssue>();
            }
        }

        private HashSet<DocumentContractuel> GetDocumentsContractuelsByIssue(long issueOid)
        {
            HashSet<DocumentContractuel> documentsContractuels = new HashSet<DocumentContractuel>();
            
            OpenConnection();
            using (var command = new MySqlCommand(@"SELECT*FROM hls_document_contractuel WHERE issue_oid = @issueOid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@issueOid", issueOid);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        documentsContractuels.Add(new DocumentContractuel
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DateDebutValidite = GetSafeDateTime(reader, "DateDebutValidite"),
                            DateFinValidite = GetSafeDateTime(reader, "DateFinValidite"),
                            Data = GetSafeJSONAsDictionary(reader, "Data"),
                            IssueOid = GetSafeLong(reader, "issue_oid"),
                            DocumentOid = GetSafeLong(reader, "document_oid"),
                            ExternalSource = new SourceOfData()
                            {
                                Oid = GetSafeLong(reader, "src_oid"),
                                Kind = GetSafeString(reader, "src_kind"),
                                Name = GetSafeString(reader, "src_name"),
                                DateUpdated = GetSafeDateTime(reader, "src_date")
                            }
                        });
                    }
                }
            }
            
            return documentsContractuels;
        }

        /// <summary>
        /// Issue courante = Source
        /// Liste = Cible
        /// </summary>
        /// <param name="oidIssue"></param>
        /// <returns></returns>
        public HashSet<AbstractIssue> GetIssueSortante(long oidIssue, int maxDepth = 0, HashSet<long> visitedIds = null)
        {
            // Initialize visitedIds if null
            visitedIds = visitedIds ?? new HashSet<long>();

            HashSet<AbstractIssue> issuesSortantes = new HashSet<AbstractIssue>();

            OpenConnection();
            using (var command = new MySqlCommand(@"
                SELECT i.* FROM hls_issue i
                INNER JOIN hls_issue_relation ihi ON i.Oid = ihi.cible_oid
                WHERE ihi.source_oid = @oidIssue", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidIssue", oidIssue);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var relatedOid = GetSafeLong(reader, "Oid");

                        // Skip if we've already processed this issue to avoid circular references
                        if (visitedIds.Contains(relatedOid))
                            continue;

                        var issueSortante = new AbstractIssue
                        {
                            Oid = relatedOid,
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes"),
                            RelationsActivite = new RelationsActivite
                            {
                                activite_oid = GetSafeLong(reader, "activite_oid"),
                                issue_parente_oid = GetSafeLong(reader, "issue_parente_oid"),
                                commanditaire_oid = GetSafeLong(reader, "commanditaire_oid"),
                                demandeur_oid = GetSafeLong(reader, "demandeur_oid"),
                                intervenant_principal_oid = GetSafeLong(reader, "intervenant_principal_oid"),
                                origine_oid = GetSafeLong(reader, "origine_oid"),
                                priorite_oid = GetSafeLong(reader, "priorite_oid"),
                                statut_oid = GetSafeLong(reader, "statut_oid"),
                                redacteur_oid = GetSafeLong(reader, "redacteur_oid")
                            }
                        };

                        issuesSortantes.Add(issueSortante);
                    }
                }
            }
            CloseConnection();

            // Fill relations for each related issue with reduced depth
            foreach (var issueSortante in issuesSortantes)
            {
                // Copy the visited IDs set to avoid modifying the original
                var relatedVisitedIds = new HashSet<long>(visitedIds);
                FillRelationFields(issueSortante, maxDepth, relatedVisitedIds);
            }
            
            return issuesSortantes;
        }
        
        /// <summary>
        /// Issue courante = cible
        /// Liste = Source
        /// </summary>
        /// <param name="oidIssue"></param>
        /// <returns></returns>
        public HashSet<AbstractIssue> GetIssueEntrante(long oidIssue, int maxDepth = 0, HashSet<long> visitedIds = null)
        {
            // Initialize visitedIds if null
            visitedIds = visitedIds ?? new HashSet<long>();

            HashSet<AbstractIssue> issueEntrante = new HashSet<AbstractIssue>();

            OpenConnection();
            using (var command = new MySqlCommand(@"
                SELECT i.* FROM hls_issue i
                INNER JOIN hls_issue_relation ihi ON i.Oid = ihi.source_oid
                WHERE ihi.cible_oid = @oidIssue", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidIssue", oidIssue);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var relatedOid = GetSafeLong(reader, "Oid");

                        // Skip if we've already processed this issue to avoid circular references
                        if (visitedIds.Contains(relatedOid))
                            continue;

                        var issueEntrant = new AbstractIssue
                        {
                            Oid = relatedOid,
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes"),
                            RelationsActivite = new RelationsActivite
                            {
                                activite_oid = GetSafeLong(reader, "activite_oid"),
                                issue_parente_oid = GetSafeLong(reader, "issue_parente_oid"),
                                commanditaire_oid = GetSafeLong(reader, "commanditaire_oid"),
                                demandeur_oid = GetSafeLong(reader, "demandeur_oid"),
                                intervenant_principal_oid = GetSafeLong(reader, "intervenant_principal_oid"),
                                origine_oid = GetSafeLong(reader, "origine_oid"),
                                priorite_oid = GetSafeLong(reader, "priorite_oid"),
                                statut_oid = GetSafeLong(reader, "statut_oid"),
                                redacteur_oid = GetSafeLong(reader, "redacteur_oid")
                            }
                        };

                        issueEntrante.Add(issueEntrant);
                    }
                }
            }
            CloseConnection();

            // Fill relations for each related issue with reduced depth
            foreach (var issueEntrant in issueEntrante)
            {
                // Copy the visited IDs set to avoid modifying the original
                var relatedVisitedIds = new HashSet<long>(visitedIds);
                FillRelationFields(issueEntrant, maxDepth, relatedVisitedIds);
            }
            
            return issueEntrante;
        }

        public override void Add(AbstractIssue entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // 1. Insert main entity
                    using (var command = new MySqlCommand("INSERT INTO hls_issue (Oid, __version, Code, Sujet, Description, DateCreation, DateModification, DatePrevisionnelleDebut, DatePrevisionnelleFin, DateEffectiveDebut, DateEffectiveFin, KindOfActivite, KindOfIssueParente, Avancement, TempsEstimeMinutes, TempsEffectifMinutes) VALUES (@Oid, @__version, @Code, @Sujet, @Description, @DateCreation, @DateModification, @DatePrevisionnelleDebut, @DatePrevisionnelleFin, @DateEffectiveDebut, @DateEffectiveFin, @KindOfActivite, @KindOfIssueParente, @Avancement, @TempsEstimeMinutes, @TempsEffectifMinutes)", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.Parameters.AddWithValue("@__version", entity.__version);
                        command.Parameters.AddWithValue("@Code", entity.Code);
                        command.Parameters.AddWithValue("@Sujet", entity.Sujet);
                        command.Parameters.AddWithValue("@Description", entity.Description);
                        command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                        command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                        command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                        command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                        command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                        command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                        command.Parameters.AddWithValue("@KindOfActivite", entity.KindOfActivite);
                        command.Parameters.AddWithValue("@KindOfIssueParente", entity.KindOfIssueParente);
                        command.Parameters.AddWithValue("@Avancement", entity.Avancement);
                        command.Parameters.AddWithValue("@TempsEstimeMinutes", entity.TempsEstimeMinutes);
                        command.Parameters.AddWithValue("@TempsEffectifMinutes", entity.TempsEffectifMinutes);
                        command.ExecuteNonQuery();
                    }

                    // 2. Handle Intervenant relationships
                    if (entity.Intervenants != null && entity.Intervenants.Any())
                    {
                        foreach (var intervenant in entity.Intervenants)
                        {
                            using (var relationCommand = new MySqlCommand(
                                "INSERT INTO hls_issue_has_intervenant (issue_oid, personne_oid) VALUES (@issueOid, @personneOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                relationCommand.Parameters.AddWithValue("@issueOid", entity.Oid);
                                relationCommand.Parameters.AddWithValue("@personneOid", intervenant.Oid);
                                relationCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    // 3. Handle PieceJointes relationships
                    if (entity.PieceJointes != null && entity.PieceJointes.Any())
                    {
                        foreach (var pieceJointe in entity.PieceJointes)
                        {
                            using (var pieceJointeCommand = new MySqlCommand(
                                "INSERT INTO hls_issue_piece_jointe (Oid, __version, Libelle, Description, Fichier, TypeMime, Taille, DateCreation, issue_oid) VALUES (@Oid, @__version, @Libelle, @Description, @Fichier, @TypeMime, @Taille, @DateCreation, @issueOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                pieceJointeCommand.Parameters.AddWithValue("@Oid", pieceJointe.Oid);
                                pieceJointeCommand.Parameters.AddWithValue("@__version", pieceJointe.__version);
                                pieceJointeCommand.Parameters.AddWithValue("@Libelle", pieceJointe.Libelle);
                                pieceJointeCommand.Parameters.AddWithValue("@Description", pieceJointe.Description);
                                pieceJointeCommand.Parameters.AddWithValue("@Fichier", pieceJointe.Fichier);
                                pieceJointeCommand.Parameters.AddWithValue("@TypeMime", pieceJointe.TypeMime);
                                pieceJointeCommand.Parameters.AddWithValue("@Taille", pieceJointe.Taille);
                                pieceJointeCommand.Parameters.AddWithValue("@DateCreation", pieceJointe.DateCreation);
                                pieceJointeCommand.Parameters.AddWithValue("@issueOid", entity.Oid);
                                pieceJointeCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        public override void Update(AbstractIssue entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // 1. Update main entity
                    using (var command = new MySqlCommand("UPDATE hls_issue SET __version = @__version, Code = @Code, Sujet = @Sujet, Description = @Description, DateCreation = @DateCreation, DateModification = @DateModification, DatePrevisionnelleDebut = @DatePrevisionnelleDebut, DatePrevisionnelleFin = @DatePrevisionnelleFin, DateEffectiveDebut = @DateEffectiveDebut, DateEffectiveFin = @DateEffectiveFin, KindOfActivite = @KindOfActivite, KindOfIssueParente = @KindOfIssueParente, Avancement = @Avancement, TempsEstimeMinutes = @TempsEstimeMinutes, TempsEffectifMinutes = @TempsEffectifMinutes WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@__version", entity.__version);
                        command.Parameters.AddWithValue("@Code", entity.Code);
                        command.Parameters.AddWithValue("@Sujet", entity.Sujet);
                        command.Parameters.AddWithValue("@Description", entity.Description);
                        command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                        command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                        command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                        command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                        command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                        command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                        command.Parameters.AddWithValue("@KindOfActivite", entity.KindOfActivite);
                        command.Parameters.AddWithValue("@KindOfIssueParente", entity.KindOfIssueParente);
                        command.Parameters.AddWithValue("@Avancement", entity.Avancement);
                        command.Parameters.AddWithValue("@TempsEstimeMinutes", entity.TempsEstimeMinutes);
                        command.Parameters.AddWithValue("@TempsEffectifMinutes", entity.TempsEffectifMinutes);
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.ExecuteNonQuery();
                    }

                    // 2. Sync Intervenant relationships (remove old, add new)
                    // First, remove existing relationships
                    using (var deleteCommand = new MySqlCommand(
                        "DELETE FROM hls_issue_has_intervenant WHERE issue_oid = @issueOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deleteCommand.Parameters.AddWithValue("@issueOid", entity.Oid);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // Then, add current relationships
                    if (entity.Intervenants != null && entity.Intervenants.Any())
                    {
                        foreach (var intervenant in entity.Intervenants)
                        {
                            using (var insertCommand = new MySqlCommand(
                                "INSERT INTO hls_issue_has_intervenant (issue_oid, personne_oid) VALUES (@issueOid, @personneOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@issueOid", entity.Oid);
                                insertCommand.Parameters.AddWithValue("@personneOid", intervenant.Oid);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    // 3. Handle PieceJointes updates (remove old, add new)
                    // First, remove existing piece jointes
                    using (var deletePieceJointesCommand = new MySqlCommand(
                        "DELETE FROM hls_issue_piece_jointe WHERE issue_oid = @issueOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deletePieceJointesCommand.Parameters.AddWithValue("@issueOid", entity.Oid);
                        deletePieceJointesCommand.ExecuteNonQuery();
                    }

                    // Then, add current piece jointes
                    if (entity.PieceJointes != null && entity.PieceJointes.Any())
                    {
                        foreach (var pieceJointe in entity.PieceJointes)
                        {
                            using (var insertPieceJointeCommand = new MySqlCommand(
                                "INSERT INTO hls_issue_piece_jointe (Oid, __version, Libelle, Description, Fichier, TypeMime, Taille, DateCreation, issue_oid) VALUES (@Oid, @__version, @Libelle, @Description, @Fichier, @TypeMime, @Taille, @DateCreation, @issueOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertPieceJointeCommand.Parameters.AddWithValue("@Oid", pieceJointe.Oid);
                                insertPieceJointeCommand.Parameters.AddWithValue("@__version", pieceJointe.__version);
                                insertPieceJointeCommand.Parameters.AddWithValue("@Libelle", pieceJointe.Libelle);
                                insertPieceJointeCommand.Parameters.AddWithValue("@Description", pieceJointe.Description);
                                insertPieceJointeCommand.Parameters.AddWithValue("@Fichier", pieceJointe.Fichier);
                                insertPieceJointeCommand.Parameters.AddWithValue("@TypeMime", pieceJointe.TypeMime);
                                insertPieceJointeCommand.Parameters.AddWithValue("@Taille", pieceJointe.Taille);
                                insertPieceJointeCommand.Parameters.AddWithValue("@DateCreation", pieceJointe.DateCreation);
                                insertPieceJointeCommand.Parameters.AddWithValue("@issueOid", entity.Oid);
                                insertPieceJointeCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        public override void Delete(AbstractIssue entity)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // First, delete relationships and related entities
                    using (var deleteIntervenantsCommand = new MySqlCommand("DELETE FROM hls_issue_has_intervenant WHERE issue_oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        deleteIntervenantsCommand.Parameters.AddWithValue("@Oid", entity.Oid);
                        deleteIntervenantsCommand.ExecuteNonQuery();
                    }

                    using (var deletePieceJointesCommand = new MySqlCommand("DELETE FROM hls_issue_piece_jointe WHERE issue_oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        deletePieceJointesCommand.Parameters.AddWithValue("@Oid", entity.Oid);
                        deletePieceJointesCommand.ExecuteNonQuery();
                    }

                    using (var deleteJournalDetailsCommand = new MySqlCommand("DELETE jd FROM hls_journal_details jd INNER JOIN hls_journal j ON jd.journal_oid = j.Oid WHERE j.issue_oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        deleteJournalDetailsCommand.Parameters.AddWithValue("@Oid", entity.Oid);
                        deleteJournalDetailsCommand.ExecuteNonQuery();
                    }

                    using (var deleteJournalCommand = new MySqlCommand("DELETE FROM hls_journal WHERE issue_oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        deleteJournalCommand.Parameters.AddWithValue("@Oid", entity.Oid);
                        deleteJournalCommand.ExecuteNonQuery();
                    }

                    // Then, delete main entity
                    using (var command = new MySqlCommand("DELETE FROM hls_issue WHERE Oid = @Oid", (MySqlConnection)_connection, transaction))
                    {
                        command.Parameters.AddWithValue("@Oid", entity.Oid);
                        command.ExecuteNonQuery();
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }

        // TODO: ✅ COMPLETED - Joined entities retrieval methods
        // Retrieval methods for related entities
        public HashSet<Personne> GetIntervenantsByIssue(long oidIssue)
        {
            HashSet<Personne> intervenants = new HashSet<Personne>();
            OpenConnection();
            using (var command = new MySqlCommand(@"
                SELECT p.* FROM hls_personne p
                INNER JOIN hls_issue_has_intervenant ihi ON p.Oid = ihi.personne_oid
                WHERE ihi.issue_oid = @oidIssue", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidIssue", oidIssue);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        intervenants.Add(new Personne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Prenom = GetSafeString(reader, "Prenom"),
                            Fonction = GetSafeString(reader, "Fonction"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone")
                        });
                    }
                }
            }
            CloseConnection();
            return intervenants;
        }

        public HashSet<IssuePieceJointe> GetPieceJointesByIssue(long oidIssue)
        {
            HashSet<IssuePieceJointe> pieceJointes = new HashSet<IssuePieceJointe>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_piece_jointe WHERE issue_oid = @oidIssue", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidIssue", oidIssue);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        pieceJointes.Add(new IssuePieceJointe
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Fichier = GetSafeString(reader, "Fichier"),
                            TypeMime = GetSafeString(reader, "TypeMime"),
                            Taille = GetSafeLong(reader, "Taille"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }
            CloseConnection();
            return pieceJointes;
        }

        public HashSet<JournalDetails> GetJournalDetailsByIssue(long oidIssue)
        {
            HashSet<JournalDetails> journalDetails = new HashSet<JournalDetails>();
            OpenConnection();
            using (var command = new MySqlCommand(@"
                SELECT jd.* FROM hls_journal_details jd
                INNER JOIN hls_journal j ON jd.journal_oid = j.Oid
                WHERE j.issue_oid = @oidIssue", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidIssue", oidIssue);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        journalDetails.Add(new JournalDetails
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            ProprieteType = GetSafeString(reader, "ProprieteType"),
                            Propriete = GetSafeString(reader, "Propriete"),
                            AncienneValeur = GetSafeString(reader, "AncienneValeur"),
                            NouvelleValeur = GetSafeString(reader, "NouvelleValeur")
                        });
                    }
                }
            }
            CloseConnection();
            return journalDetails;
        }

        // TODO: ✅ COMPLETED - Relationship management methods
        // Relationship management methods for Intervenants
        public void AddIntervenantToIssue(long oidIssue, long oidPersonne)
        {
            OpenConnection();
            using (var command = new MySqlCommand(
                "INSERT IGNORE INTO hls_issue_has_intervenant (issue_oid, personne_oid) VALUES (@issueOid, @personneOid)",
                (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@issueOid", oidIssue);
                command.Parameters.AddWithValue("@personneOid", oidPersonne);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public void RemoveIntervenantFromIssue(long oidIssue, long oidPersonne)
        {
            OpenConnection();
            using (var command = new MySqlCommand(
                "DELETE FROM hls_issue_has_intervenant WHERE issue_oid = @issueOid AND personne_oid = @personneOid",
                (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@issueOid", oidIssue);
                command.Parameters.AddWithValue("@personneOid", oidPersonne);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public void SyncIntervenantsForIssue(long oidIssue, HashSet<long> personneOids)
        {
            OpenConnection();
            using (var transaction = ((MySqlConnection)_connection).BeginTransaction())
            {
                try
                {
                    // Remove all existing relationships
                    using (var deleteCommand = new MySqlCommand(
                        "DELETE FROM hls_issue_has_intervenant WHERE issue_oid = @issueOid",
                        (MySqlConnection)_connection, transaction))
                    {
                        deleteCommand.Parameters.AddWithValue("@issueOid", oidIssue);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // Add new relationships
                    if (personneOids != null && personneOids.Any())
                    {
                        foreach (var oidPersonne in personneOids)
                        {
                            using (var insertCommand = new MySqlCommand(
                                "INSERT INTO hls_issue_has_intervenant (issue_oid, personne_oid) VALUES (@issueOid, @personneOid)",
                                (MySqlConnection)_connection, transaction))
                            {
                                insertCommand.Parameters.AddWithValue("@issueOid", oidIssue);
                                insertCommand.Parameters.AddWithValue("@personneOid", oidPersonne);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            CloseConnection();
        }
    }
}